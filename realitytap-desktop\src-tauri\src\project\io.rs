// Core project I/O operations
use crate::{
    error::{<PERSON><PERSON><PERSON>, Result},
    models::Project,
};
use std::fs::{create_dir_all, File};
use std::io::{BufReader, Write};
use std::path::Path;
use std::sync::Mutex;

// Constants for directory and file names
pub const HAPTICS_DIR_NAME: &str = "haptics";
pub const AUDIO_DIR_NAME: &str = "audio";
pub const VIDEO_DIR_NAME: &str = "video";
pub const PROJECT_FILE_NAME: &str = "project.json";

// Global mutex for thread-safe project file writing
static PROJECT_WRITE_MUTEX: Mutex<()> = Mutex::new(());

/// Read project.json from a directory and parse it into a Project struct
pub fn read_project_directory(project_dir_path: &Path) -> Result<Project> {
    let project_file_path = project_dir_path.join(PROJECT_FILE_NAME);

    if !project_file_path.exists() {
        return Err(Error::NotFound(format!(
            "Project file not found: {:?}",
            project_file_path
        )));
    }

    let file = File::open(&project_file_path)
        .map_err(|e| Error::Io(format!("Failed to open project file: {}", e)))?;

    let reader = BufReader::new(file);
    let project: Project = serde_json::from_reader(reader)
        .map_err(|e| Error::Io(format!("Failed to parse project file: {}", e)))?;

    Ok(project)
}

/// Write a Project struct to project.json in the specified directory
pub fn write_project_directory(project: &Project, project_dir_path: &Path) -> Result<()> {
    // Ensure the project directory exists
    create_dir_all(project_dir_path)
        .map_err(|e| Error::Io(format!("Failed to create project directory: {}", e)))?;

    // Ensure haptics, audio, and video subdirectories exist
    let haptics_dir = project_dir_path.join(HAPTICS_DIR_NAME);
    let audio_dir = project_dir_path.join(AUDIO_DIR_NAME);
    let video_dir = project_dir_path.join(VIDEO_DIR_NAME);

    create_dir_all(&haptics_dir)
        .map_err(|e| Error::Io(format!("Failed to create haptics directory: {}", e)))?;

    create_dir_all(&audio_dir)
        .map_err(|e| Error::Io(format!("Failed to create audio directory: {}", e)))?;

    create_dir_all(&video_dir)
        .map_err(|e| Error::Io(format!("Failed to create video directory: {}", e)))?;

    // Thread-safe project file writing with JSON validation and retry mechanism
    let project_file_path = project_dir_path.join(PROJECT_FILE_NAME);

    // Acquire global write lock to ensure thread safety
    let _lock = PROJECT_WRITE_MUTEX.lock().map_err(|e| {
        Error::Io(format!("Failed to acquire project write lock: {}", e))
    })?;

    const MAX_RETRIES: u32 = 3;
    let mut last_error = None;

    for attempt in 1..=MAX_RETRIES {
        match write_project_with_validation(project, &project_file_path) {
            Ok(()) => {
                log::info!("Successfully wrote project file on attempt {}", attempt);
                return Ok(());
            }
            Err(e) => {
                log::warn!("Attempt {} failed to write project file: {}", attempt, e);
                last_error = Some(e);

                if attempt < MAX_RETRIES {
                    // Wait a bit before retrying
                    std::thread::sleep(std::time::Duration::from_millis(100));
                }
            }
        }
    }

    // All retries failed
    Err(last_error.unwrap_or_else(|| {
        Error::Io("Failed to write project file after all retries".to_string())
    }))
}

/// Write project with JSON validation
fn write_project_with_validation(project: &Project, project_file_path: &Path) -> Result<()> {
    // Step 1: Serialize to JSON string
    let json_content = serde_json::to_string_pretty(project)
        .map_err(|e| Error::Io(format!("Failed to serialize project: {}", e)))?;

    // Step 2: Validate JSON format by parsing it back
    let _: serde_json::Value = serde_json::from_str(&json_content)
        .map_err(|e| Error::Io(format!("Generated JSON is invalid: {}", e)))?;

    // Step 3: Additional validation - try to deserialize back to Project
    let _: Project = serde_json::from_str(&json_content)
        .map_err(|e| Error::Io(format!("Generated JSON cannot be deserialized back to Project: {}", e)))?;

    // Step 4: Write to file with explicit UTF-8 encoding
    let mut file = File::create(project_file_path)
        .map_err(|e| Error::Io(format!("Failed to create project file: {}", e)))?;

    // Write UTF-8 content (Rust strings are already UTF-8)
    // Note: We don't add BOM for JSON files as it's not recommended for JSON format
    file.write_all(json_content.as_bytes())
        .map_err(|e| Error::Io(format!("Failed to write project file: {}", e)))?;

    // Step 5: Ensure all data is written to disk
    file.flush()
        .map_err(|e| Error::Io(format!("Failed to flush project file: {}", e)))?;

    file.sync_all()
        .map_err(|e| Error::Io(format!("Failed to sync project file: {}", e)))?;

    // Step 6: Final validation - read back and verify
    drop(file); // Close the file handle

    let verification_content = std::fs::read_to_string(project_file_path)
        .map_err(|e| Error::Io(format!("Failed to read back project file for verification: {}", e)))?;

    let _: Project = serde_json::from_str(&verification_content)
        .map_err(|e| Error::Io(format!("Written file failed verification: {}", e)))?;

    Ok(())
}
