{"name": "realitytap-studio", "private": true, "version": "1.0.8", "type": "module", "scripts": {"dev": "vite", "dev:debug": "cross-env VITE_LOG_LEVEL=DEBUG VITE_ENABLE_PERFORMANCE_LOG=true VITE_ENABLE_DRAG_LOG=true vite", "dev:quiet": "cross-env VITE_LOG_LEVEL=WARN vite", "build": "vue-tsc -b && vite build", "build:debug": "cross-env VITE_LOG_LEVEL=DEBUG vue-tsc -b && vite build", "build:with-updater": "pwsh -ExecutionPolicy Bypass -File scripts/build-with-updater.ps1", "build:with-updater:debug": "pwsh -ExecutionPolicy Bypass -File scripts/build-with-updater.ps1 -Configuration debug", "generate:update-files": "pwsh -ExecutionPolicy Bypass -File scripts/generate-update-files.ps1", "generate:update-files:debug": "pwsh -ExecutionPolicy Bypass -File scripts/generate-update-files.ps1 -Configuration debug", "setup:signing": "pwsh -ExecutionPolicy Bypass -File scripts/setup-signing-env.ps1", "generate:demo-keys": "scripts\\generate-demo-keys.bat", "generate:demo-keys:ps1": "pwsh -ExecutionPolicy Bypass -File scripts/generate-demo-keys.ps1", "build:production": "pwsh -ExecutionPolicy Bypass -File scripts/build-production.ps1", "build:production:load-env": "pwsh -ExecutionPolicy Bypass -File scripts/build-production.ps1 -LoadEnvFile", "build:interactive": "pwsh -ExecutionPolicy Bypass -File scripts/build-interactive.ps1", "test:build-env": "pwsh -ExecutionPolicy Bypass -File scripts/test-build-environment.ps1", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev -c src-tauri/tauri.dev.conf.json", "tauri:dev:debug": "cross-env VITE_LOG_LEVEL=DEBUG VITE_ENABLE_PERFORMANCE_LOG=true VITE_ENABLE_DRAG_LOG=true tauri dev", "tauri:dev:quiet": "cross-env VITE_LOG_LEVEL=WARN tauri dev", "tauri:build": "tauri build", "tauri:build:debug": "cross-env VITE_LOG_LEVEL=DEBUG tauri build", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui"}, "dependencies": {"@realitytap/shared": "file:../realitytap-shared", "@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-dialog": "^2.3.2", "@tauri-apps/plugin-fs": "^2.4.1", "@tauri-apps/plugin-os": "^2.3.0", "@tauri-apps/plugin-process": "^2.3.0", "@tauri-apps/plugin-updater": "^2.9.0", "@types/uuid": "^10.0.0", "@vicons/fluent": "^0.13.0", "@vicons/ionicons5": "^0.13.0", "@vicons/material": "^0.13.0", "naive-ui": "^2.42.0", "path": "^0.12.7", "pinia": "^3.0.3", "uuid": "^11.1.0", "vfonts": "^0.0.3", "vue": "^3.5.18", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1", "zod": "^4.0.14"}, "devDependencies": {"@tauri-apps/cli": "^2.7.1", "@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.1", "@vitest/ui": "^3.2.4", "@vue/tsconfig": "^0.7.0", "cross-env": "^10.0.0", "jsdom": "^26.1.0", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.6", "vite-node": "^3.2.4", "vitest": "^3.2.4", "vue-tsc": "^3.0.4"}}