// RealityTap 应用配置类型定义 - 简化版本
// Application configuration types for RealityTap Desktop - Simplified

import { SamplingRateType } from "./haptic-types";

/**
 * PlayEffectDialog 配置 - 只保存用户选择
 */
export interface PlayEffectDialogConfig {
  /** 最后选择的马达ID */
  lastSelectedMotorId: string | null;
  /** 最后选择的采样率 */
  lastSelectedSamplingRate: SamplingRateType;
}

/**
 * 应用配置根接口 - 简化版本
 */
export interface AppConfig {
  /** 配置文件版本 */
  version: string;
  /** 最后修改时间 */
  lastModified: string;
  /** PlayEffectDialog 配置 */
  playEffectDialog: PlayEffectDialogConfig;
}

/**
 * 配置更新事件类型
 */
export type ConfigUpdateEvent = {
  /** 更新的配置路径 */
  path: string;
  /** 旧值 */
  oldValue: any;
  /** 新值 */
  newValue: any;
  /** 更新时间 */
  timestamp: string;
};

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * 配置操作选项
 */
export interface ConfigOperationOptions {
  /** 是否创建备份 */
  createBackup?: boolean;
  /** 是否验证配置 */
  validate?: boolean;
  /** 是否触发事件 */
  emitEvents?: boolean;
  /** 操作超时时间 (毫秒) */
  timeout?: number;
}
