/**
 * LOD (Level of Detail) 渲染系统
 * 根据振幅特征和视图状态动态调整渲染细节级别
 */

import { audioLogger } from "@/utils/logger/logger";

/**
 * LOD级别定义
 */
export enum LODLevel {
  ULTRA_HIGH = 0,  // 超高细节：1:1采样，适用于极近距离查看
  HIGH = 1,        // 高细节：1:2采样，适用于近距离查看
  MEDIUM = 2,      // 中等细节：1:4采样，适用于标准查看
  LOW = 3,         // 低细节：1:8采样，适用于远距离查看
  ULTRA_LOW = 4,   // 超低细节：1:16采样，适用于概览模式
}

/**
 * LOD配置接口
 */
export interface LODConfig {
  level: LODLevel;
  samplingRatio: number;      // 采样比例 (0-1)
  maxSampleCount: number;     // 最大样本数
  performancePriority: boolean; // 是否优先性能
  qualityThreshold: number;   // 质量阈值
}

/**
 * LOD渲染策略
 */
export const LOD_STRATEGIES: Record<LODLevel, LODConfig> = {
  [LODLevel.ULTRA_HIGH]: {
    level: LODLevel.ULTRA_HIGH,
    samplingRatio: 1.0,
    maxSampleCount: 4096,
    performancePriority: false,
    qualityThreshold: 0.95,
  },
  [LODLevel.HIGH]: {
    level: LODLevel.HIGH,
    samplingRatio: 0.5,
    maxSampleCount: 2048,
    performancePriority: false,
    qualityThreshold: 0.85,
  },
  [LODLevel.MEDIUM]: {
    level: LODLevel.MEDIUM,
    samplingRatio: 0.25,
    maxSampleCount: 1024,
    performancePriority: true,
    qualityThreshold: 0.7,
  },
  [LODLevel.LOW]: {
    level: LODLevel.LOW,
    samplingRatio: 0.125,
    maxSampleCount: 512,
    performancePriority: true,
    qualityThreshold: 0.5,
  },
  [LODLevel.ULTRA_LOW]: {
    level: LODLevel.ULTRA_LOW,
    samplingRatio: 0.0625,
    maxSampleCount: 256,
    performancePriority: true,
    qualityThreshold: 0.3,
  },
};

/**
 * LOD选择条件
 */
export interface LODSelectionCriteria {
  amplitudeCategory: 'ultra-low' | 'low' | 'medium' | 'high' | 'ultra-high';
  performanceRisk: 'minimal' | 'low' | 'medium' | 'high' | 'critical';
  zoomLevel: number;           // 缩放级别 (0.1 - 10.0)
  viewportWidth: number;       // 视口宽度
  sampleCount: number;         // 原始样本数
  isRealTimeRendering: boolean; // 是否实时渲染
}

/**
 * 智能LOD级别选择器
 */
export function selectOptimalLODLevel(criteria: LODSelectionCriteria): LODLevel {
  const { amplitudeCategory, performanceRisk, zoomLevel, viewportWidth, sampleCount, isRealTimeRendering } = criteria;
  
  // 基础LOD级别评分
  let lodScore = 0;
  
  // 1. 根据性能风险调整
  switch (performanceRisk) {
    case 'critical':
      lodScore += 4; // 强制使用低LOD
      break;
    case 'high':
      lodScore += 3;
      break;
    case 'medium':
      lodScore += 2;
      break;
    case 'low':
      lodScore += 1;
      break;
    case 'minimal':
      lodScore += 0;
      break;
  }
  
  // 2. 根据振幅类别调整
  switch (amplitudeCategory) {
    case 'ultra-high':
      lodScore += 2; // 高振幅需要更保守的LOD
      break;
    case 'high':
      lodScore += 1;
      break;
    case 'medium':
      lodScore += 0;
      break;
    case 'low':
      lodScore -= 1;
      break;
    case 'ultra-low':
      lodScore -= 2; // 低振幅可以使用更高的LOD
      break;
  }
  
  // 3. 根据缩放级别调整
  if (zoomLevel > 5.0) {
    lodScore -= 2; // 高缩放需要更高细节
  } else if (zoomLevel > 2.0) {
    lodScore -= 1;
  } else if (zoomLevel < 0.5) {
    lodScore += 2; // 低缩放可以使用低细节
  } else if (zoomLevel < 1.0) {
    lodScore += 1;
  }
  
  // 4. 根据视口宽度调整
  if (viewportWidth < 800) {
    lodScore += 1; // 小屏幕可以使用低细节
  } else if (viewportWidth > 1920) {
    lodScore -= 1; // 大屏幕需要更高细节
  }
  
  // 5. 实时渲染调整
  if (isRealTimeRendering) {
    lodScore += 1; // 实时渲染优先性能
  }
  
  // 6. 样本数量调整
  if (sampleCount > 8192) {
    lodScore += 1; // 大量样本需要降低LOD
  } else if (sampleCount < 1024) {
    lodScore -= 1; // 少量样本可以提高LOD
  }
  
  // 将评分转换为LOD级别
  const clampedScore = Math.max(0, Math.min(4, lodScore));
  return clampedScore as LODLevel;
}

/**
 * 计算LOD采样策略
 */
export function calculateLODSampling(
  originalSamples: number[],
  lodLevel: LODLevel
): { samples: number[]; indices: number[] } {
  const config = LOD_STRATEGIES[lodLevel];
  const originalCount = originalSamples.length;
  const targetCount = Math.min(config.maxSampleCount, Math.floor(originalCount * config.samplingRatio));
  
  if (targetCount >= originalCount) {
    // 不需要采样，返回原始数据
    return {
      samples: originalSamples,
      indices: originalSamples.map((_, i) => i),
    };
  }
  
  const step = originalCount / targetCount;
  const sampledData: number[] = [];
  const sampledIndices: number[] = [];
  
  for (let i = 0; i < targetCount; i++) {
    const index = Math.floor(i * step);
    sampledData.push(originalSamples[index]);
    sampledIndices.push(index);
  }
  
  return {
    samples: sampledData,
    indices: sampledIndices,
  };
}

/**
 * LOD性能分析
 */
export function analyzeLODPerformance(
  originalSampleCount: number,
  lodLevel: LODLevel,
  sampledCount: number
): {
  reductionRatio: number;
  estimatedSpeedup: string;
  memoryReduction: string;
  qualityImpact: string;
} {
  const config = LOD_STRATEGIES[lodLevel];
  const reductionRatio = 1 - (sampledCount / originalSampleCount);
  
  // 估算性能提升
  const speedupMultiplier = 1 / config.samplingRatio;
  const estimatedSpeedup = `${Math.round((speedupMultiplier - 1) * 100)}%`;
  
  // 估算内存减少
  const memoryReduction = `${Math.round(reductionRatio * 100)}%`;
  
  // 质量影响评估
  let qualityImpact: string;
  if (config.qualityThreshold > 0.9) {
    qualityImpact = "极小影响";
  } else if (config.qualityThreshold > 0.8) {
    qualityImpact = "轻微影响";
  } else if (config.qualityThreshold > 0.6) {
    qualityImpact = "中等影响";
  } else {
    qualityImpact = "明显影响";
  }
  
  audioLogger.debug("🎯 LOD性能分析", {
    LOD级别: LODLevel[lodLevel],
    原始样本数: originalSampleCount,
    采样后样本数: sampledCount,
    减少比例: `${Math.round(reductionRatio * 100)}%`,
    预估性能提升: estimatedSpeedup,
    内存减少: memoryReduction,
    质量影响: qualityImpact,
    配置: config,
  });
  
  return {
    reductionRatio,
    estimatedSpeedup,
    memoryReduction,
    qualityImpact,
  };
}

/**
 * LOD缓存键生成器
 */
export function generateLODCacheKey(
  fileId: string,
  lodLevel: LODLevel,
  amplitudeScale: number
): string {
  return `lod_${fileId}_${lodLevel}_${amplitudeScale.toFixed(2)}`;
}
