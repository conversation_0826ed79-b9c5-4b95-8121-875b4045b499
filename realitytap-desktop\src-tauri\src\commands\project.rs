// Project-level commands
use crate::{
    commands::app_data::add_to_recent_projects,
    error::{Error, Result},
    models,
    project,
    utils::validation::validate_project_name,
};
use chrono::Utc;
use serde::Serialize;
use std::fs;
use std::path::PathBuf;
use tauri;
use uuid::Uuid;

/// Response structure for project rename operation
#[derive(Serialize)]
pub struct RenameProjectResponse {
    pub new_project_name: String,
    pub new_project_dir_path: String,
}

#[tauri::command]
pub async fn load_project(project_dir_path: String) -> Result<models::Project> {
    log::info!("加载项目目录: {}", project_dir_path);
    let path = PathBuf::from(&project_dir_path);

    let project = project::read_project_directory(&path)?;

    // Add to recent projects after successful load
    add_to_recent_projects(&project, &project_dir_path)?;

    Ok(project)
}

#[tauri::command]
pub async fn save_project(project_data: models::Project, project_dir_path: String) -> Result<()> {
    let path = PathBuf::from(&project_dir_path);

    let mut updated_project = project_data;
    updated_project.last_modified_time = Utc::now();

    let result = project::write_project_directory(&updated_project, &path);

    if result.is_ok() {
        // Update recent projects after successful save
        add_to_recent_projects(&updated_project, &project_dir_path)?;
    }

    result
}

#[tauri::command]
pub async fn create_new_project(
    project_name: String,
    author: String,
    description: String,
    target_dir: String,
) -> Result<models::Project> {
    log::info!("创建新项目 '{}' 在目录 '{}'", project_name, target_dir);

    let project_root_path = PathBuf::from(&target_dir);

    if project_root_path.exists() {
        if fs::read_dir(&project_root_path)?.next().is_some() {
            return Err(Error::Io(format!(
                "项目目录 '{:?}' 已存在且不为空.",
                project_root_path
            )));
        }
    }

    // Create the main project directory
    fs::create_dir_all(&project_root_path).map_err(|e| {
        Error::Io(format!(
            "无法创建项目根目录 '{:?}': {}",
            project_root_path, e
        ))
    })?;

    let now = Utc::now();
    let proj_uuid = Uuid::new_v4();
    let new_project: models::Project = models::Project {
        project_uuid: proj_uuid,
        project_name: project_name.clone(),
        create_time: now,
        last_modified_time: now,
        author,
        version: "1.0.0".to_string(),
        description,
        tags: vec![],
        groups: vec![],
        files: vec![],
    };

    // Create an initial empty project.json
    project::write_project_directory(&new_project, &project_root_path)?;

    log::info!(
        "新项目 '{}' 创建成功于 '{:?}'",
        project_name,
        project_root_path
    );
    Ok(new_project)
}

#[tauri::command]
pub async fn rename_project(
    project_path: String,
    new_project_name: String,
) -> Result<RenameProjectResponse> {
    log::info!(
        "正在尝试重命名项目文件夹 '{}' 为 '{}'",
        project_path,
        new_project_name
    );

    // Validate input path exists
    let current_path = PathBuf::from(&project_path);
    if !current_path.exists() || !current_path.is_dir() {
        return Err(Error::PathNotFound(format!(
            "项目路径不存在或不是目录: {}",
            project_path
        )));
    }

    // Validate and sanitize project name
    let validated_name = validate_project_name(&new_project_name)?;

    // Get parent directory
    let parent_dir = match current_path.parent() {
        Some(parent) => parent,
        None => return Err(Error::PathNotFound("无法获取项目的父目录".to_string())),
    };

    // Calculate new project directory path
    let new_path = parent_dir.join(&validated_name);

    // Check if target path already exists (and is not the same)
    if new_path.exists() && new_path != current_path {
        return Err(Error::NameAlreadyExists(format!(
            "目标目录已存在: {}",
            new_path.display()
        )));
    }

    // Perform directory rename operation
    match fs::rename(&current_path, &new_path) {
        Ok(_) => {
            log::info!(
                "目录重命名成功: {} -> {}",
                current_path.display(),
                new_path.display()
            );
        }
        Err(e) => {
            log::error!(
                "重命名目录失败: {} -> {}, Error: {}",
                current_path.display(),
                new_path.display(),
                e
            );
            return Err(Error::RenameIoError(format!("重命名目录失败: {}", e)));
        }
    }

    // Construct response
    let response = RenameProjectResponse {
        new_project_name: validated_name,
        new_project_dir_path: new_path.to_str().unwrap_or_default().to_string(),
    };

    log::info!(
        "项目文件夹重命名成功: {} -> {}",
        project_path,
        response.new_project_dir_path
    );

    Ok(response)
}
