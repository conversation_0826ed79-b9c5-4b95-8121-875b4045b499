{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4014864309271236696, "build_script_build", false, 10859411557067227145], [10755362358622467486, "build_script_build", false, 4434827960730790436], [5157003953992891593, "build_script_build", false, 18005654607185726737], [13592916204794590741, "build_script_build", false, 7794747229026094033], [7236291379133587555, "build_script_build", false, 6524805581574974761], [12676100885892732016, "build_script_build", false, 17427154853465628288], [17509843537913359226, "build_script_build", false, 15238635187464230619], [1582828171158827377, "build_script_build", false, 11437634551128229682], [11721252211900136025, "build_script_build", false, 8130051704739473615]], "local": [{"RerunIfChanged": {"output": "debug\\build\\realitytap_studio-f3cc15fdb2c8a3f9\\output", "paths": ["ffmpeg/windows/x64", "libs/windows/x64", "tauri.conf.json", "capabilities", "librtcore.dll", "librtssl.dll", "librtutils.dll", "libgcc_s_seh-1.dll", "libstdc++-6.dll", "libwinpthread-1.dll", "motors\\LRA_0809_normal_170Hz.conf", "motors\\LRA_0809_pro_170Hz.conf", "motors\\LRA_0916_normal_170Hz.conf", "ffmpeg.exe", "ffprobe.exe"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}