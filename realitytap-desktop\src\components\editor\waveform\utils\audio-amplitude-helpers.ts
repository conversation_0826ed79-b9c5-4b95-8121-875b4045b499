/**
 * 音频振幅显示优化工具函数
 * 提供振幅缩放、增强和显示效果的相关工具
 */

// 音频振幅缩放配置接口
export interface AmplitudeScaleConfig {
  amplitudeScale: number; // 基础缩放比例 (0-1)
  amplitudeBoost: number; // 增强倍数
  maxHeightUsage: number; // 最大高度使用比例 (0-1)
  enableAdaptiveScaling?: boolean; // 是否启用自适应缩放
}

// 预设的振幅显示配置
export const AMPLITUDE_PRESETS = {
  // 【新增】超保守模式：专为超高振幅音频优化，最小化性能开销
  ultraConservative: {
    amplitudeScale: 0.3,
    amplitudeBoost: 0.7,
    maxHeightUsage: 0.5,
    enableAdaptiveScaling: false,
  },

  // 保守模式：适合高振幅音频
  conservative: {
    amplitudeScale: 0.5,
    amplitudeBoost: 1.0,
    maxHeightUsage: 0.7,
    enableAdaptiveScaling: false,
  },

  // 标准模式：平衡的显示效果
  standard: {
    amplitudeScale: 0.75,
    amplitudeBoost: 1.5,
    maxHeightUsage: 0.9,
    enableAdaptiveScaling: true,
  },

  // 增强模式：适合低振幅音频，强调线性马达效果
  enhanced: {
    amplitudeScale: 0.85,
    amplitudeBoost: 2.0,
    maxHeightUsage: 0.95,
    enableAdaptiveScaling: true,
  },

  // 极致模式：最大化显示效果
  maximum: {
    amplitudeScale: 1.0,
    amplitudeBoost: 2.5,
    maxHeightUsage: 0.98,
    enableAdaptiveScaling: true,
  },

  // 【新增】性能优先模式：专为高频交互场景优化
  performanceOptimized: {
    amplitudeScale: 0.6,
    amplitudeBoost: 1.2,
    maxHeightUsage: 0.8,
    enableAdaptiveScaling: false,
  },
} as const;

/**
 * 计算优化的振幅缩放因子
 * @param maxAmplitude 最大振幅值
 * @param graphHeight 图形区域高度
 * @param config 缩放配置
 * @returns 最终的缩放因子
 */
export function calculateOptimizedAmplitudeScale(
  maxAmplitude: number,
  graphHeight: number,
  config: AmplitudeScaleConfig
): number {
  if (maxAmplitude <= 0 || graphHeight <= 0) return 1;

  const { amplitudeScale, amplitudeBoost, maxHeightUsage, enableAdaptiveScaling } = config;

  // 基础缩放：使用配置的比例
  let baseScale = (graphHeight * amplitudeScale) / maxAmplitude;

  // 自适应缩放：根据振幅大小调整增强倍数
  let finalBoost = amplitudeBoost;
  if (enableAdaptiveScaling) {
    // 对于小振幅信号，应用更大的增强倍数
    // 振幅越小，增强倍数越大，但有上限
    const amplitudeRatio = Math.min(maxAmplitude, 1.0); // 假设1.0为标准振幅
    const adaptiveMultiplier = Math.max(0.5, Math.min(2.0, 1.0 / Math.sqrt(amplitudeRatio)));
    finalBoost = amplitudeBoost * adaptiveMultiplier;
  }

  // 应用增强倍数
  let finalScale = baseScale * finalBoost;

  // 限制最大缩放，避免超出画布范围
  const maxAllowedScale = (graphHeight * maxHeightUsage) / maxAmplitude;
  finalScale = Math.min(finalScale, maxAllowedScale);

  return finalScale;
}

/**
 * 获取振幅显示效果的描述信息
 * @param finalScale 最终缩放因子
 * @param maxAmplitude 最大振幅
 * @param graphHeight 图形高度
 * @returns 显示效果描述
 */
export function getAmplitudeDisplayInfo(
  finalScale: number,
  maxAmplitude: number,
  graphHeight: number
): {
  heightUsagePercent: string;
  effectiveAmplitude: number;
  scalingFactor: number;
  displayQuality: 'low' | 'medium' | 'high' | 'excellent';
} {
  const effectiveAmplitude = finalScale * maxAmplitude;
  const heightUsagePercent = ((effectiveAmplitude / graphHeight) * 100).toFixed(1) + '%';
  const scalingFactor = finalScale;

  // 根据高度使用率判断显示质量
  const usageRatio = effectiveAmplitude / graphHeight;
  let displayQuality: 'low' | 'medium' | 'high' | 'excellent';

  if (usageRatio < 0.3) {
    displayQuality = 'low';
  } else if (usageRatio < 0.6) {
    displayQuality = 'medium';
  } else if (usageRatio < 0.8) {
    displayQuality = 'high';
  } else {
    displayQuality = 'excellent';
  }

  return {
    heightUsagePercent,
    effectiveAmplitude,
    scalingFactor,
    displayQuality,
  };
}

/**
 * 振幅特征分析结果接口
 */
export interface AmplitudeCharacteristics {
  amplitudeCategory: 'ultra-low' | 'low' | 'medium' | 'high' | 'ultra-high';
  performanceRisk: 'minimal' | 'low' | 'medium' | 'high' | 'critical';
  recommendedPreset: keyof typeof AMPLITUDE_PRESETS;
  optimizedConfig: AmplitudeScaleConfig;
  analysisDetails: {
    maxAmplitude: number;
    amplitudeRange: number;
    avgAmplitude: number;
    dynamicRange: number;
  };
}

/**
 * 分析音频振幅特征并推荐最优配置
 * @param data 音频振幅数据
 * @returns 振幅特征分析结果
 */
export function analyzeAmplitudeCharacteristics(data: {
  max_amplitude: number;
  min_amplitude: number;
  samples: number[];
}): AmplitudeCharacteristics {
  const { max_amplitude, min_amplitude, samples } = data;

  // 计算基础特征
  const maxAmp = Math.max(Math.abs(max_amplitude), Math.abs(min_amplitude));
  const amplitudeRange = Math.abs(max_amplitude - min_amplitude);
  const avgAmplitude = amplitudeRange / 2;

  // 计算动态范围（样本标准差）
  const sampleMean = samples.reduce((sum, sample) => sum + Math.abs(sample), 0) / samples.length;
  const variance = samples.reduce((sum, sample) => sum + Math.pow(Math.abs(sample) - sampleMean, 2), 0) / samples.length;
  const dynamicRange = Math.sqrt(variance);

  // 分类振幅级别
  let amplitudeCategory: AmplitudeCharacteristics['amplitudeCategory'];
  let performanceRisk: AmplitudeCharacteristics['performanceRisk'];

  if (maxAmp >= 0.9) {
    amplitudeCategory = 'ultra-high';
    performanceRisk = 'critical';
  } else if (maxAmp >= 0.7) {
    amplitudeCategory = 'high';
    performanceRisk = 'high';
  } else if (maxAmp >= 0.4) {
    amplitudeCategory = 'medium';
    performanceRisk = 'medium';
  } else if (maxAmp >= 0.2) {
    amplitudeCategory = 'low';
    performanceRisk = 'low';
  } else {
    amplitudeCategory = 'ultra-low';
    performanceRisk = 'minimal';
  }

  // 根据特征选择最优配置
  let optimizedConfig: AmplitudeScaleConfig;
  let recommendedPreset: keyof typeof AMPLITUDE_PRESETS;

  switch (amplitudeCategory) {
    case 'ultra-high':
      // 超高振幅：极度保守策略，最小化边界检查
      recommendedPreset = 'conservative';
      optimizedConfig = {
        amplitudeScale: 0.4,  // 更保守的缩放
        amplitudeBoost: 0.8,  // 减少增强
        maxHeightUsage: 0.6,  // 限制最大高度使用
        enableAdaptiveScaling: false, // 禁用自适应缩放
      };
      break;

    case 'high':
      // 高振幅：保守策略
      recommendedPreset = 'conservative';
      optimizedConfig = {
        amplitudeScale: 0.5,
        amplitudeBoost: 1.0,
        maxHeightUsage: 0.7,
        enableAdaptiveScaling: false,
      };
      break;

    case 'medium':
      // 中等振幅：标准策略
      recommendedPreset = 'standard';
      optimizedConfig = {
        amplitudeScale: 0.75,
        amplitudeBoost: 1.5,
        maxHeightUsage: 0.9,
        enableAdaptiveScaling: true,
      };
      break;

    case 'low':
      // 低振幅：增强策略
      recommendedPreset = 'enhanced';
      optimizedConfig = {
        amplitudeScale: 0.85,
        amplitudeBoost: 2.0,
        maxHeightUsage: 0.95,
        enableAdaptiveScaling: true,
      };
      break;

    case 'ultra-low':
      // 超低振幅：最大增强策略
      recommendedPreset = 'maximum';
      optimizedConfig = {
        amplitudeScale: 1.0,
        amplitudeBoost: 2.5,
        maxHeightUsage: 0.98,
        enableAdaptiveScaling: true,
      };
      break;
  }

  return {
    amplitudeCategory,
    performanceRisk,
    recommendedPreset,
    optimizedConfig,
    analysisDetails: {
      maxAmplitude: maxAmp,
      amplitudeRange,
      avgAmplitude,
      dynamicRange,
    },
  };
}

/**
 * 根据音频特征推荐最佳的振幅显示配置
 * @param maxAmplitude 最大振幅
 * @param minAmplitude 最小振幅
 * @param sampleCount 采样点数
 * @returns 推荐的配置名称
 */
export function recommendAmplitudePreset(
  maxAmplitude: number,
  minAmplitude: number,
  _sampleCount: number
): keyof typeof AMPLITUDE_PRESETS {
  const amplitudeRange = Math.abs(maxAmplitude - minAmplitude);
  const avgAmplitude = amplitudeRange / 2;

  // 根据平均振幅推荐配置
  if (avgAmplitude > 0.8) {
    return 'conservative'; // 高振幅音频使用保守配置
  } else if (avgAmplitude > 0.5) {
    return 'standard'; // 中等振幅使用标准配置
  } else if (avgAmplitude > 0.2) {
    return 'enhanced'; // 低振幅使用增强配置
  } else {
    return 'maximum'; // 极低振幅使用最大配置
  }
}

/**
 * 创建自定义振幅配置
 * @param basePreset 基础预设
 * @param overrides 覆盖的配置项
 * @returns 自定义配置
 */
export function createCustomAmplitudeConfig(
  basePreset: keyof typeof AMPLITUDE_PRESETS,
  overrides: Partial<AmplitudeScaleConfig>
): AmplitudeScaleConfig {
  return {
    ...AMPLITUDE_PRESETS[basePreset],
    ...overrides,
  };
}
