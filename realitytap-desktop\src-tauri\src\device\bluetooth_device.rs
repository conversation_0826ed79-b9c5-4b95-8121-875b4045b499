// Bluetooth device implementation

use crate::device::manager::DeviceInterface;
use crate::error::Result;
use crate::models::device::*;
use async_trait::async_trait;

pub struct BluetoothDevice {
    device_id: String,
    mac_address: String,
    connected: bool,
}

impl BluetoothDevice {
    pub fn new(device_id: String, mac_address: String) -> Self {
        Self {
            device_id,
            mac_address,
            connected: false,
        }
    }
}

#[async_trait]
impl DeviceInterface for BluetoothDevice {
    async fn connect(&mut self) -> Result<()> {
        // TODO: Implement Bluetooth connection
        log::info!("Connecting to Bluetooth device: {} ({})", self.device_id, self.mac_address);
        self.connected = true;
        Ok(())
    }

    async fn disconnect(&mut self) -> Result<()> {
        // TODO: Implement Bluetooth disconnection
        log::info!("Disconnecting from Bluetooth device: {}", self.device_id);
        self.connected = false;
        Ok(())
    }

    async fn is_connected(&self) -> bool {
        self.connected
    }

    async fn send_data(&mut self, data: &[u8]) -> Result<()> {
        // TODO: Implement Bluetooth data transmission
        log::info!("Sending {} bytes to Bluetooth device: {}", data.len(), self.device_id);
        Ok(())
    }

    async fn get_device_info(&self) -> Result<DeviceDiscoveryInfo> {
        // TODO: Get actual Bluetooth device info
        Ok(DeviceDiscoveryInfo {
            device_type: DeviceType::Bluetooth,
            identifier: self.device_id.clone(),
            name: Some(format!("Bluetooth Device {} ({})", self.device_id, self.mac_address)),
            metadata: Some(DeviceMetadata {
                connection_info: Some(ConnectionInfo {
                    mac_address: Some(self.mac_address.clone()),
                    ..Default::default()
                }),
                ..Default::default()
            }),
        })
    }

    async fn get_status(&self) -> DeviceStatus {
        if self.connected {
            DeviceStatus::Connected
        } else {
            DeviceStatus::Disconnected
        }
    }
}
