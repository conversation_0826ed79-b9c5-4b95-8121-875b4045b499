// FFmpeg-based audio processing utilities
use crate::error::{<PERSON><PERSON><PERSON>, Result};
use crate::models::audio::AudioInfo;
use log;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::process::Command;
use std::fs;
use std::env;
use tempfile::NamedTempFile;

/// FFprobe stream information
#[derive(Debug, Deserialize, Serialize)]
pub struct FFprobeStream {
    pub codec_type: String,
    pub codec_name: Option<String>,
    pub sample_rate: Option<String>,
    pub channels: Option<u32>,
    pub duration: Option<String>,
    pub bit_rate: Option<String>,
}

/// FFprobe format information
#[derive(Debug, Deserialize, Serialize)]
pub struct FFprobeFormat {
    pub duration: Option<String>,
    pub bit_rate: Option<String>,
    pub nb_streams: u32,
}

/// FFprobe output structure
#[derive(Debug, Deserialize, Serialize)]
pub struct FFprobeOutput {
    pub streams: Vec<FFprobeStream>,
    pub format: FFprobeFormat,
}

/// Audio normalization strategy
#[derive(Debug, Clone)]
pub enum NormalizationStrategy {
    /// Peak normalization: scale maximum absolute value to target range
    Peak { target_peak: f32 },
    /// RMS normalization: normalize based on root mean square value
    Rms { target_rms: f32 },
    /// Dynamic range compression: intelligently compress excessive dynamic range
    DynamicRange { threshold: f32, ratio: f32 },
}

/// Audio quality information after processing
#[derive(Debug, Clone, Serialize)]
pub struct AudioQualityInfo {
    pub peak_amplitude: f32,
    pub rms_level: f32,
    pub dynamic_range: f32,
    pub clipping_detected: bool,
    pub normalization_applied: bool,
    pub original_peak: f32,
    pub normalization_factor: f32,
}

/// Get the path to the bundled ffprobe executable
fn get_ffprobe_path() -> String {
    let exe_ext = if cfg!(target_os = "windows") { ".exe" } else { "" };
    let exe_name = format!("ffprobe{}", exe_ext);

    // Try to get the executable directory (for both dev and production)
    if let Ok(current_exe) = env::current_exe() {
        if let Some(exe_dir) = current_exe.parent() {
            // First try: same directory as executable (production)
            let bundled_path = exe_dir.join(&exe_name);
            if bundled_path.exists() {
                log::info!("使用预编译的 ffprobe (生产环境): {}", bundled_path.display());
                return bundled_path.to_string_lossy().to_string();
            }

            // Second try: resources directory (alternative production layout)
            let resources_path = exe_dir.join("resources").join(&exe_name);
            if resources_path.exists() {
                log::info!("使用预编译的 ffprobe (资源目录): {}", resources_path.display());
                return resources_path.to_string_lossy().to_string();
            }
        }
    }

    // Fallback to system ffprobe
    log::info!("使用系统 ffprobe");
    "ffprobe".to_string()
}

/// Get the path to the bundled ffmpeg executable
fn get_ffmpeg_path() -> String {
    let exe_ext = if cfg!(target_os = "windows") { ".exe" } else { "" };
    let exe_name = format!("ffmpeg{}", exe_ext);

    // Try to get the executable directory (for both dev and production)
    if let Ok(current_exe) = env::current_exe() {
        if let Some(exe_dir) = current_exe.parent() {
            // First try: same directory as executable (production)
            let bundled_path = exe_dir.join(&exe_name);
            if bundled_path.exists() {
                log::info!("使用预编译的 ffmpeg (生产环境): {}", bundled_path.display());
                return bundled_path.to_string_lossy().to_string();
            }

            // Second try: resources directory (alternative production layout)
            let resources_path = exe_dir.join("resources").join(&exe_name);
            if resources_path.exists() {
                log::info!("使用预编译的 ffmpeg (资源目录): {}", resources_path.display());
                return resources_path.to_string_lossy().to_string();
            }
        }
    }

    // Fallback to system ffmpeg
    log::info!("使用系统 ffmpeg");
    "ffmpeg".to_string()
}

/// Analyze audio quality and detect potential issues
pub fn analyze_audio_quality(samples: &[f32]) -> AudioQualityInfo {
    if samples.is_empty() {
        return AudioQualityInfo {
            peak_amplitude: 0.0,
            rms_level: 0.0,
            dynamic_range: 0.0,
            clipping_detected: false,
            normalization_applied: false,
            original_peak: 0.0,
            normalization_factor: 1.0,
        };
    }

    // Calculate peak amplitude
    let peak_amplitude = samples.iter()
        .map(|&x| x.abs())
        .fold(0.0f32, f32::max);

    // Calculate RMS level
    let sum_squares: f32 = samples.iter()
        .map(|&x| x * x)
        .sum();
    let rms_level = (sum_squares / samples.len() as f32).sqrt();

    // Calculate dynamic range (difference between max and min)
    let max_val = samples.iter().cloned().fold(f32::NEG_INFINITY, f32::max);
    let min_val = samples.iter().cloned().fold(f32::INFINITY, f32::min);
    let dynamic_range = max_val - min_val;

    // Detect clipping (values at or near the limits)
    let clipping_threshold = 0.99;
    let clipping_detected = samples.iter()
        .any(|&x| x.abs() >= clipping_threshold);

    AudioQualityInfo {
        peak_amplitude,
        rms_level,
        dynamic_range,
        clipping_detected,
        normalization_applied: false,
        original_peak: peak_amplitude,
        normalization_factor: 1.0,
    }
}

/// Normalize audio samples using the specified strategy
pub fn normalize_audio_samples(
    mut samples: Vec<f32>,
    strategy: NormalizationStrategy
) -> (Vec<f32>, AudioQualityInfo) {
    let mut quality_info = analyze_audio_quality(&samples);

    if samples.is_empty() {
        return (samples, quality_info);
    }

    let normalization_factor = match strategy {
        NormalizationStrategy::Peak { target_peak } => {
            if quality_info.peak_amplitude > 0.0 {
                target_peak / quality_info.peak_amplitude
            } else {
                1.0
            }
        },
        NormalizationStrategy::Rms { target_rms } => {
            if quality_info.rms_level > 0.0 {
                target_rms / quality_info.rms_level
            } else {
                1.0
            }
        },
        NormalizationStrategy::DynamicRange { threshold, ratio } => {
            if quality_info.peak_amplitude > threshold {
                // Apply compression above threshold
                let excess = quality_info.peak_amplitude - threshold;
                let compressed_excess = excess / ratio;
                (threshold + compressed_excess) / quality_info.peak_amplitude
            } else {
                1.0
            }
        }
    };

    // Apply normalization if needed
    if (normalization_factor - 1.0).abs() > 0.001 {
        for sample in &mut samples {
            *sample *= normalization_factor;
        }

        quality_info.normalization_applied = true;
        quality_info.normalization_factor = normalization_factor;

        // Recalculate quality info after normalization
        let new_peak = samples.iter().map(|&x| x.abs()).fold(0.0f32, f32::max);
        quality_info.peak_amplitude = new_peak;

        log::info!("音频归一化完成: 原始峰值={:.4}, 归一化因子={:.4}, 新峰值={:.4}",
                   quality_info.original_peak, normalization_factor, new_peak);
    } else {
        log::info!("音频数据已在正常范围内，无需归一化: 峰值={:.4}", quality_info.peak_amplitude);
    }

    (samples, quality_info)
}

/// Check if ffprobe is available (bundled or system)
pub fn check_ffprobe_available() -> bool {
    let ffprobe_path = get_ffprobe_path();
    match Command::new(&ffprobe_path).arg("-version").output() {
        Ok(output) => output.status.success(),
        Err(_) => false,
    }
}

/// Check if ffmpeg is available (bundled or system)
pub fn check_ffmpeg_available() -> bool {
    let ffmpeg_path = get_ffmpeg_path();
    match Command::new(&ffmpeg_path).arg("-version").output() {
        Ok(output) => output.status.success(),
        Err(_) => false,
    }
}

/// Extract audio information from MP4 file using ffprobe
pub fn extract_mp4_info_with_ffprobe(abs_path: &PathBuf) -> Result<Option<AudioInfo>> {
    log::info!("使用 ffprobe 提取 MP4 音频信息: {}", abs_path.display());

    // Check if ffprobe is available
    if !check_ffprobe_available() {
        return Err(Error::Io(
            "ffprobe 未找到。请安装 FFmpeg 工具包。\n\
            下载地址: https://ffmpeg.org/download.html".to_string()
        ));
    }

    // Run ffprobe command using the appropriate path
    let ffprobe_path = get_ffprobe_path();
    let output = Command::new(&ffprobe_path)
        .arg("-v")
        .arg("quiet")
        .arg("-print_format")
        .arg("json")
        .arg("-show_format")
        .arg("-show_streams")
        .arg(abs_path.as_os_str())
        .output()
        .map_err(|e| Error::Io(format!("执行 ffprobe 命令失败: {}", e)))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(Error::Io(format!("ffprobe 执行失败: {}", stderr)));
    }

    // Parse JSON output
    let stdout = String::from_utf8_lossy(&output.stdout);
    let probe_result: FFprobeOutput = serde_json::from_str(&stdout)
        .map_err(|e| Error::Io(format!("解析 ffprobe 输出失败: {}", e)))?;

    // Find the first audio stream
    let audio_stream = probe_result.streams
        .iter()
        .find(|stream| stream.codec_type == "audio");

    if let Some(stream) = audio_stream {
        // Extract duration (prefer stream duration, fallback to format duration)
        let duration_str = stream.duration
            .as_ref()
            .or(probe_result.format.duration.as_ref());

        let duration_ms = if let Some(duration_str) = duration_str {
            match duration_str.parse::<f64>() {
                Ok(duration_seconds) => (duration_seconds * 1000.0) as u64,
                Err(_) => {
                    log::warn!("无法解析音频时长: {}", duration_str);
                    0
                }
            }
        } else {
            log::warn!("未找到音频时长信息");
            0
        };

        // Extract sample rate
        let sample_rate = if let Some(sample_rate_str) = &stream.sample_rate {
            match sample_rate_str.parse::<u32>() {
                Ok(rate) => rate,
                Err(_) => {
                    log::warn!("无法解析采样率: {}", sample_rate_str);
                    44100 // Default fallback
                }
            }
        } else {
            log::warn!("未找到采样率信息，使用默认值 44100");
            44100
        };

        let audio_info = AudioInfo {
            duration_ms,
            sample_rate,
        };

        log::info!("ffprobe 提取音频信息成功: {:?}", audio_info);
        Ok(Some(audio_info))
    } else {
        log::warn!("MP4 文件中未找到音频流");
        Ok(None)
    }
}

/// Extract complete audio data from MP4 file using ffmpeg
pub fn extract_mp4_audio_data_with_ffmpeg(abs_path: &PathBuf) -> Result<(Vec<f32>, AudioInfo)> {
    log::info!("使用 ffmpeg 提取 MP4 完整音频数据: {}", abs_path.display());

    // First get audio info
    let audio_info = extract_mp4_info_with_ffprobe(abs_path)?
        .ok_or_else(|| Error::Io("无法获取 MP4 音频信息".to_string()))?;

    // Check if ffmpeg is available
    if !check_ffmpeg_available() {
        return Err(Error::Io(
            "ffmpeg 未找到。请安装 FFmpeg 工具包。\n\
            下载地址: https://ffmpeg.org/download.html".to_string()
        ));
    }

    // Create temporary file for raw audio output
    let temp_file = NamedTempFile::new()
        .map_err(|e| Error::Io(format!("创建临时文件失败: {}", e)))?;
    let temp_path = temp_file.path();

    // Extract audio as raw f32 PCM data using the appropriate path
    let ffmpeg_path = get_ffmpeg_path();
    let output = Command::new(&ffmpeg_path)
        .arg("-i")
        .arg(abs_path.as_os_str())
        .arg("-vn") // No video
        .arg("-acodec")
        .arg("pcm_f32le") // 32-bit float PCM, little endian
        .arg("-f")
        .arg("f32le") // Raw f32 format
        .arg("-y") // Overwrite output file
        .arg(temp_path.as_os_str())
        .output()
        .map_err(|e| Error::Io(format!("执行 ffmpeg 命令失败: {}", e)))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(Error::Io(format!("ffmpeg 音频提取失败: {}", stderr)));
    }

    // Read the raw audio data
    let raw_data = fs::read(temp_path)
        .map_err(|e| Error::Io(format!("读取临时音频文件失败: {}", e)))?;

    // Convert bytes to f32 samples
    let mut samples = Vec::with_capacity(raw_data.len() / 4);
    for chunk in raw_data.chunks_exact(4) {
        let sample = f32::from_le_bytes([chunk[0], chunk[1], chunk[2], chunk[3]]);
        samples.push(sample);
    }

    log::info!("ffmpeg 原始音频数据提取成功，样本数: {}", samples.len());

    // Analyze audio quality before normalization
    let original_quality = analyze_audio_quality(&samples);
    log::info!("原始音频质量分析: 峰值={:.4}, RMS={:.4}, 动态范围={:.4}, 削波检测={}",
               original_quality.peak_amplitude,
               original_quality.rms_level,
               original_quality.dynamic_range,
               original_quality.clipping_detected);

    // Apply normalization if the peak amplitude exceeds normal range
    let (normalized_samples, quality_info) = if original_quality.peak_amplitude > 1.0 {
        log::warn!("检测到音频峰值超出正常范围 (>{:.4})，应用峰值归一化", original_quality.peak_amplitude);
        normalize_audio_samples(samples, NormalizationStrategy::Peak { target_peak: 0.95 })
    } else if original_quality.peak_amplitude < 0.1 {
        log::info!("检测到音频信号较弱 (<{:.4})，应用适度增益", original_quality.peak_amplitude);
        normalize_audio_samples(samples, NormalizationStrategy::Peak { target_peak: 0.7 })
    } else {
        log::info!("音频峰值在正常范围内 ({:.4})，无需归一化", original_quality.peak_amplitude);
        (samples, original_quality)
    };

    // Log final quality information
    if quality_info.normalization_applied {
        log::info!("音频归一化完成: 原始峰值={:.4} -> 最终峰值={:.4}, 归一化因子={:.4}",
                   quality_info.original_peak,
                   quality_info.peak_amplitude,
                   quality_info.normalization_factor);
    }

    log::info!("MP4 音频数据处理完成，最终样本数: {}", normalized_samples.len());
    Ok((normalized_samples, audio_info))
}

/// Validate audio data quality and log detailed information
pub fn validate_and_log_audio_quality(samples: &[f32], file_path: &str) -> AudioQualityInfo {
    let quality_info = analyze_audio_quality(samples);

    log::info!("=== 音频质量检测报告 ===");
    log::info!("文件路径: {}", file_path);
    log::info!("样本数量: {}", samples.len());
    log::info!("峰值幅度: {:.6}", quality_info.peak_amplitude);
    log::info!("RMS 电平: {:.6}", quality_info.rms_level);
    log::info!("动态范围: {:.6}", quality_info.dynamic_range);
    log::info!("削波检测: {}", if quality_info.clipping_detected { "是" } else { "否" });

    // Additional quality checks
    let zero_samples = samples.iter().filter(|&&x| x == 0.0).count();
    let zero_percentage = (zero_samples as f64 / samples.len() as f64) * 100.0;
    log::info!("零值样本: {} ({:.2}%)", zero_samples, zero_percentage);

    // Check for potential issues
    if quality_info.peak_amplitude > 1.0 {
        log::warn!("⚠️  音频峰值超出标准范围 (>{:.4})，可能导致失真", quality_info.peak_amplitude);
    }

    if quality_info.peak_amplitude < 0.01 {
        log::warn!("⚠️  音频信号极弱 (<{:.4})，可能影响播放效果", quality_info.peak_amplitude);
    }

    if quality_info.clipping_detected {
        log::warn!("⚠️  检测到音频削波，可能存在失真");
    }

    if zero_percentage > 50.0 {
        log::warn!("⚠️  零值样本过多 ({:.1}%)，可能存在静音段或数据问题", zero_percentage);
    }

    log::info!("=== 音频质量检测完成 ===");

    quality_info
}

/// Get recommended normalization strategy based on audio characteristics
pub fn get_recommended_normalization_strategy(quality_info: &AudioQualityInfo) -> Option<NormalizationStrategy> {
    if quality_info.peak_amplitude > 1.0 {
        // Peak exceeds normal range - use peak normalization
        Some(NormalizationStrategy::Peak { target_peak: 0.95 })
    } else if quality_info.peak_amplitude < 0.1 {
        // Signal too weak - boost to reasonable level
        Some(NormalizationStrategy::Peak { target_peak: 0.7 })
    } else if quality_info.dynamic_range > 3.0 {
        // Excessive dynamic range - use compression
        Some(NormalizationStrategy::DynamicRange {
            threshold: 0.8,
            ratio: 2.0
        })
    } else {
        // Audio is within acceptable range
        None
    }
}