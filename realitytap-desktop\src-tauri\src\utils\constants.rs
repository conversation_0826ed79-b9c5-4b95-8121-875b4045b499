// Common constants used across the application

// File and directory names
pub const RECENT_PROJECTS_FILE: &str = ".recent_projects.json";
pub const WINDOW_SETTINGS_FILE: &str = ".window_settings.json";

// Window size constraints
pub const MIN_WINDOW_WIDTH: f64 = 1280.0;
pub const MIN_WINDOW_HEIGHT: f64 = 980.0;
pub const MAX_WINDOW_WIDTH: f64 = 4096.0;
pub const MAX_WINDOW_HEIGHT: f64 = 2160.0;

// Recent projects limit
pub const MAX_RECENT_PROJECTS: usize = 10;

// Audio formats
pub const AUDIO_FORMATS_FOR_SEARCH: &[&str] = &["wav", "mp3", "ogg", "mid"];

// File system validation
pub const INVALID_FILENAME_CHARS: &[char] = &['/', '\\', ':', '*', '?', '"', '<', '>', '|'];

// Platform-specific file name length limits
pub const MAX_FILENAME_LENGTH_WINDOWS: usize = 255;
pub const MAX_FILENAME_LENGTH_UNIX: usize = 255;


// Reserved file names on Windows
pub const WINDOWS_RESERVED_NAMES: &[&str] = &[
    "CON", "PRN", "AUX", "NUL",
    "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
    "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
];

// Additional problematic characters for cross-platform compatibility
pub const PROBLEMATIC_FILENAME_CHARS: &[char] = &['\0', '\t', '\n', '\r'];

// Control characters (0x00-0x1F and 0x7F-0x9F)
pub const CONTROL_CHAR_RANGES: &[(u32, u32)] = &[(0x00, 0x1F), (0x7F, 0x9F)];
