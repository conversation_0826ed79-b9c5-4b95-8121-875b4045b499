// RealityTap 触觉反馈库 FFI 绑定

use std::os::raw::{c_char, c_void};
use std::sync::{Arc, Mutex};
use std::path::PathBuf;
use libloading::Library;
use once_cell::sync::Lazy;
use crate::haptic::error::{HapticError, HapticResult};

// Windows-specific imports for registry access
#[cfg(windows)]
use winreg::enums::*;
#[cfg(windows)]
use winreg::RegKey;

/// 线性马达驱动芯片采样频率类型枚举
#[repr(C)]
#[derive(Debug, Clone, Copy)]
pub enum SamplingRateType {
    Sampling6Khz = 4,   // 6KHz驱动芯片采样率，基础效果
    Sampling8Khz = 5,   // 8KHz驱动芯片采样率，通用选择
    Sampling12Khz = 6,  // 12KHz驱动芯片采样率，中等精度
    Sampling24Khz = 8,  // 24KHz驱动芯片采样率，高精度效果
}

impl From<u32> for SamplingRateType {
    fn from(value: u32) -> Self {
        match value {
            4 => SamplingRateType::Sampling6Khz,
            5 => SamplingRateType::Sampling8Khz,
            6 => SamplingRateType::Sampling12Khz,
            8 => SamplingRateType::Sampling24Khz,
            _ => SamplingRateType::Sampling8Khz, // 默认值
        }
    }
}

/// 波形处理状态枚举
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq)]
#[allow(dead_code)]
pub enum WaveformProcessingStatus {
    ProcessingError = 0xFF9,    // 处理错误（超时）
    ProcessingStopped = 0xFF4,  // 处理已停止
    ProcessingActive = 0xFF2,   // 正在处理中
    ProcessingReady = 0xFF0,    // 系统就绪，处理完成
}

/// 触觉执行器参数结构体
#[repr(C)]
pub struct HapticActuatorParams {
    pub id: u32,                           // 设备唯一标识符
    pub f0: u32,                           // 线性马达实际驱动频率 (Hz)
    pub file: *const c_char,               // 配置文件路径
    pub sampling_rate: SamplingRateType,   // 驱动芯片采样率类型
    pub output_handler: *mut c_void,       // 输出处理器指针
}

/// IHapticOutputHandler 接口的 C 函数指针类型
pub type OnHapticOutputStartFn = extern "C" fn(*mut c_void);
pub type OnHapticOutputCompleteFn = extern "C" fn(*mut c_void);
pub type OnHapticOutputStopFn = extern "C" fn(*mut c_void);
pub type OnWaveformChunkStartFn = extern "C" fn(*mut c_void);
pub type ProcessWaveformSampleFn = extern "C" fn(*mut c_void, c_char);
pub type WaitChunkProcessingCompleteFn = extern "C" fn(*mut c_void, i32) -> i32;
pub type SetOutputAmplitudeFn = extern "C" fn(*mut c_void, u32);

/// 输出处理器虚函数表
#[repr(C)]
pub struct HapticOutputHandlerVTable {
    pub on_haptic_output_start: OnHapticOutputStartFn,
    pub on_haptic_output_complete: OnHapticOutputCompleteFn,
    pub on_haptic_output_stop: OnHapticOutputStopFn,
    pub on_waveform_chunk_start: OnWaveformChunkStartFn,
    pub process_waveform_sample: ProcessWaveformSampleFn,
    pub wait_chunk_processing_complete: WaitChunkProcessingCompleteFn,
    pub set_output_amplitude: SetOutputAmplitudeFn,
}

/// 输出处理器对象
#[repr(C)]
pub struct HapticOutputHandlerObject {
    pub vtable: *const HapticOutputHandlerVTable,
    pub data: *mut c_void,
}

/// RealityTap 核心库 API 函数类型定义
pub type InitFn = unsafe extern "C" fn(*mut HapticActuatorParams, isize) -> i32;
pub type ReinitFn = unsafe extern "C" fn(*mut HapticActuatorParams, isize) -> i32;
pub type SetAmplitudeFn = unsafe extern "C" fn(u32) -> i32;
pub type StopVibrationFn = unsafe extern "C" fn() -> i32;
pub type PlayEffectFn = unsafe extern "C" fn(i32, i32) -> i32;
pub type PlayRtpFn = unsafe extern "C" fn(i32, *mut u32) -> i32;
pub type PlayEnvelopeFn = unsafe extern "C" fn(*const c_char, c_char) -> i32;
pub type PlayHapticsFn = unsafe extern "C" fn(*const i32, u32) -> i32;
pub type AppendInitFn = unsafe extern "C" fn() -> i32;
pub type AppendStartFn = unsafe extern "C" fn() -> i32;
pub type AppendEnvelopeFn = unsafe extern "C" fn(*const i32, u32, bool) -> i32;
pub type AppendParamFn = unsafe extern "C" fn(i32, i32, i32) -> i32;
pub type AppendHapticsFn = unsafe extern "C" fn(*const i32, u32, u32, u32, i32, i32) -> i32;
pub type AppendPrebakFn = unsafe extern "C" fn(u32, u32) -> i32;
pub type AppendRtpFn = unsafe extern "C" fn(i32) -> i32;
pub type AppendStopFn = unsafe extern "C" fn() -> i32;
pub type AppendOnFn = unsafe extern "C" fn(u32) -> i32;

/// RealityTap 核心库 API 结构体
pub struct RealityTapApi {
    pub init: InitFn,
    #[allow(dead_code)]
    pub reinit: ReinitFn,
    pub set_amplitude: SetAmplitudeFn,
    #[allow(dead_code)]
    pub stop_vibration: StopVibrationFn,
    #[allow(dead_code)]
    pub play_effect: PlayEffectFn,
    #[allow(dead_code)]
    pub play_rtp: PlayRtpFn,
    #[allow(dead_code)]
    pub play_envelope: PlayEnvelopeFn,
    #[allow(dead_code)]
    pub play_haptics: PlayHapticsFn,
    pub append_init: AppendInitFn,
    pub append_start: AppendStartFn,
    #[allow(dead_code)]
    pub append_envelope: AppendEnvelopeFn,
    #[allow(dead_code)]
    pub append_param: AppendParamFn,
    pub append_haptics: AppendHapticsFn,
    #[allow(dead_code)]
    pub append_prebak: AppendPrebakFn,
    #[allow(dead_code)]
    pub append_rtp: AppendRtpFn,
    pub append_stop: AppendStopFn,
    #[allow(dead_code)]
    pub append_on: AppendOnFn,
}

/// 全局库实例
static LIBRARY: Lazy<Arc<Mutex<Option<Library>>>> = Lazy::new(|| Arc::new(Mutex::new(None)));
static API: Lazy<Arc<Mutex<Option<RealityTapApi>>>> = Lazy::new(|| Arc::new(Mutex::new(None)));

/// 从Windows注册表获取安装目录
#[cfg(windows)]
fn get_install_dir_from_registry() -> Option<PathBuf> {
    match RegKey::predef(HKEY_CURRENT_USER).open_subkey("Software\\AWA\\RealityTap Haptics Studio") {
        Ok(key) => {
            // 首先尝试读取 InstallDir 键
            if let Ok(install_dir) = key.get_value::<String, _>("InstallDir") {
                log::debug!("从注册表读取到安装目录 (InstallDir): {}", install_dir);
                return Some(PathBuf::from(install_dir));
            }

            // 如果 InstallDir 不存在，尝试读取默认值（兼容旧版本安装器）
            if let Ok(install_dir) = key.get_value::<String, _>("") {
                log::debug!("从注册表读取到安装目录 (默认值): {}", install_dir);
                return Some(PathBuf::from(install_dir));
            }

            log::warn!("注册表项存在但无法读取安装目录值");
            None
        }
        Err(e) => {
            log::debug!("无法打开注册表项: {}", e);
            None
        }
    }
}

/// 尝试从相对路径加载库
fn try_load_library_relative() -> Result<Library, String> {
    let lib_path = "librtcore.dll";
    log::debug!("尝试从相对路径加载库: {}", lib_path);

    unsafe {
        Library::new(lib_path).map_err(|e| {
            format!("相对路径加载失败 {}: {}", lib_path, e)
        })
    }
}

/// 尝试从可执行文件目录加载库
fn try_load_library_from_exe_dir() -> Result<Library, String> {
    match std::env::current_exe() {
        Ok(exe_path) => {
            if let Some(exe_dir) = exe_path.parent() {
                let lib_path = exe_dir.join("librtcore.dll");
                log::debug!("尝试从可执行文件目录加载库: {}", lib_path.display());

                unsafe {
                    Library::new(&lib_path).map_err(|e| {
                        format!("可执行文件目录加载失败 {}: {}", lib_path.display(), e)
                    })
                }
            } else {
                Err("无法获取可执行文件目录".to_string())
            }
        }
        Err(e) => Err(format!("无法获取可执行文件路径: {}", e))
    }
}

/// 尝试从注册表安装目录加载库 (仅Windows)
#[cfg(windows)]
fn try_load_library_from_registry() -> Result<Library, String> {
    if let Some(install_dir) = get_install_dir_from_registry() {
        let lib_path = install_dir.join("librtcore.dll");
        log::debug!("尝试从注册表安装目录加载库: {}", lib_path.display());

        unsafe {
            Library::new(&lib_path).map_err(|e| {
                format!("注册表安装目录加载失败 {}: {}", lib_path.display(), e)
            })
        }
    } else {
        Err("无法从注册表获取安装目录".to_string())
    }
}

/// 尝试从注册表安装目录加载库 (非Windows平台)
#[cfg(not(windows))]
fn try_load_library_from_registry() -> Result<Library, String> {
    Err("注册表功能仅在Windows平台可用".to_string())
}

/// 将相对路径转换为基于可执行文件目录的绝对路径
/// 这解决了 MSI 安装后自动启动时工作目录不正确的问题
pub fn resolve_config_file_path(relative_path: &str) -> HapticResult<String> {
    // 获取可执行文件路径
    let exe_path = std::env::current_exe()
        .map_err(|e| HapticError::PathResolution(format!("无法获取可执行文件路径: {}", e)))?;

    // 获取可执行文件所在目录
    let exe_dir = exe_path.parent()
        .ok_or_else(|| HapticError::PathResolution("无法获取可执行文件目录".to_string()))?;

    // 构建绝对路径
    let absolute_path = exe_dir.join(relative_path);

    // 检查文件是否存在
    if !absolute_path.exists() {
        log::warn!("配置文件不存在: {}", absolute_path.display());
        // 不直接返回错误，让 librtcore 自己处理文件不存在的情况
    }

    // 转换为字符串
    absolute_path.to_str()
        .ok_or_else(|| HapticError::PathResolution("路径包含无效字符".to_string()))
        .map(|s| {
            log::debug!("配置文件路径解析: {} -> {}", relative_path, s);
            s.to_string()
        })
}

/// 使用多种策略尝试加载库
fn try_load_library_with_fallback() -> HapticResult<Library> {
    let mut errors = Vec::new();

    // 策略1: 尝试相对路径（保持向后兼容）
    match try_load_library_relative() {
        Ok(library) => {
            log::info!("成功从相对路径加载 librtcore.dll");
            return Ok(library);
        }
        Err(e) => {
            log::debug!("相对路径加载失败: {}", e);
            errors.push(format!("相对路径: {}", e));
        }
    }

    // 策略2: 尝试从可执行文件目录加载
    match try_load_library_from_exe_dir() {
        Ok(library) => {
            log::info!("成功从可执行文件目录加载 librtcore.dll");
            return Ok(library);
        }
        Err(e) => {
            log::debug!("可执行文件目录加载失败: {}", e);
            errors.push(format!("可执行文件目录: {}", e));
        }
    }

    // 策略3: 尝试从注册表安装目录加载 (仅Windows)
    #[cfg(windows)]
    {
        match try_load_library_from_registry() {
            Ok(library) => {
                log::info!("成功从注册表安装目录加载 librtcore.dll");
                return Ok(library);
            }
            Err(e) => {
                log::debug!("注册表安装目录加载失败: {}", e);
                errors.push(format!("注册表安装目录: {}", e));
            }
        }
    }

    // 所有策略都失败了
    let error_summary = errors.join("; ");
    log::error!("所有DLL加载策略都失败了: {}", error_summary);
    Err(HapticError::DllLoadFailed(format!(
        "无法加载 librtcore.dll，尝试了以下路径都失败: {}",
        error_summary
    )))
}

/// 加载 RealityTap 核心库
pub fn load_library() -> HapticResult<()> {
    let mut lib_guard = LIBRARY.lock().map_err(|e| {
        HapticError::ThreadSyncError(format!("获取库锁失败: {}", e))
    })?;

    if lib_guard.is_some() {
        return Ok(()); // 已经加载
    }

    log::info!("开始加载 librtcore.dll");

    // 使用多策略尝试加载库
    let library = try_load_library_with_fallback()?;

    // 加载所有 API 函数
    let api = load_api_functions(&library)?;

    *lib_guard = Some(library);
    drop(lib_guard);

    let mut api_guard = API.lock().map_err(|e| {
        HapticError::ThreadSyncError(format!("获取API锁失败: {}", e))
    })?;
    *api_guard = Some(api);

    log::info!("librtcore.dll 加载成功，所有API函数已绑定");
    Ok(())
}

/// 从库中加载所有 API 函数
fn load_api_functions(library: &Library) -> HapticResult<RealityTapApi> {
    unsafe {
        Ok(RealityTapApi {
            init: *library.get(b"awa_realitytap_init\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_init: {}", e)))?,
            reinit: *library.get(b"awa_realitytap_reinit\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_reinit: {}", e)))?,
            set_amplitude: *library.get(b"awa_realitytap_set_amplitude\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_set_amplitude: {}", e)))?,
            stop_vibration: *library.get(b"awa_realitytap_stop_vibration\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_stop_vibration: {}", e)))?,
            play_effect: *library.get(b"awa_realitytap_play_effect\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_play_effect: {}", e)))?,
            play_rtp: *library.get(b"awa_realitytap_play_rtp\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_play_rtp: {}", e)))?,
            play_envelope: *library.get(b"awa_realitytap_play_envelope\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_play_envelope: {}", e)))?,
            play_haptics: *library.get(b"awa_realitytap_play_haptics\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_play_haptics: {}", e)))?,
            append_init: *library.get(b"awa_realitytap_append_init\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_append_init: {}", e)))?,
            append_start: *library.get(b"awa_realitytap_append_start\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_append_start: {}", e)))?,
            append_envelope: *library.get(b"awa_realitytap_append_envelope\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_append_envelope: {}", e)))?,
            append_param: *library.get(b"awa_realitytap_append_param\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_append_param: {}", e)))?,
            append_haptics: *library.get(b"awa_realitytap_append_haptics\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_append_haptics: {}", e)))?,
            append_prebak: *library.get(b"awa_realitytap_append_prebak\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_append_prebak: {}", e)))?,
            append_rtp: *library.get(b"awa_realitytap_append_rtp\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_append_rtp: {}", e)))?,
            append_stop: *library.get(b"awa_realitytap_append_stop\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_append_stop: {}", e)))?,
            append_on: *library.get(b"awa_realitytap_append_on\0")
                .map_err(|e| HapticError::SymbolNotFound(format!("awa_realitytap_append_on: {}", e)))?,
        })
    }
}

/// 获取 API 实例
pub fn get_api() -> HapticResult<Arc<Mutex<Option<RealityTapApi>>>> {
    Ok(API.clone())
}

/// 卸载库
pub fn unload_library() -> HapticResult<()> {
    log::info!("开始卸载 librtcore 库");

    let mut lib_guard = LIBRARY.lock().map_err(|e| {
        HapticError::ThreadSyncError(format!("获取库锁失败: {}", e))
    })?;

    let mut api_guard = API.lock().map_err(|e| {
        HapticError::ThreadSyncError(format!("获取API锁失败: {}", e))
    })?;

    // 清空 API 实例
    *api_guard = None;
    log::debug!("API 实例已清空");

    // 卸载库
    if lib_guard.is_some() {
        *lib_guard = None;
        log::info!("librtcore 库已卸载");
    } else {
        log::debug!("librtcore 库未加载，无需卸载");
    }

    Ok(())
}

/// 检查库是否已加载
pub fn is_library_loaded() -> bool {
    if let Ok(lib_guard) = LIBRARY.lock() {
        lib_guard.is_some()
    } else {
        false
    }
}

/// 完全重新加载 DLL
///
/// 这个函数会：
/// 1. 安全地卸载当前的 librtcore.dll
/// 2. 等待一段时间确保 DLL 完全卸载
/// 3. 重新加载 DLL 并获取所有函数指针
/// 4. 提供详细的日志记录和错误处理
/// 5. 验证重新加载的完整性
pub fn reload_library_completely() -> HapticResult<()> {
    log::info!("开始完全重新加载 librtcore 库");

    // 1. 检查当前状态
    let was_loaded = is_library_loaded();
    log::debug!("当前库加载状态: {}", if was_loaded { "已加载" } else { "未加载" });

    // 2. 如果已加载，先卸载
    if was_loaded {
        log::info!("卸载现有的 librtcore 库");
        unload_library().map_err(|e| {
            log::error!("卸载库失败: {}", e);
            HapticError::DllLoadFailed(format!("卸载库失败: {}", e))
        })?;

        // 验证卸载是否成功
        if is_library_loaded() {
            log::error!("库卸载后验证失败，库仍然处于加载状态");
            return Err(HapticError::DllLoadFailed(
                "库卸载后验证失败".to_string()
            ));
        }

        // 3. 等待确保 DLL 完全卸载
        log::debug!("等待 DLL 完全卸载...");
        std::thread::sleep(std::time::Duration::from_millis(150));
    }

    // 4. 重新加载库
    log::info!("重新加载 librtcore 库");
    load_library().map_err(|e| {
        log::error!("重新加载库失败: {}", e);
        HapticError::DllLoadFailed(format!("重新加载库失败: {}", e))
    })?;

    // 5. 验证加载是否成功
    if !is_library_loaded() {
        log::error!("DLL 重新加载后验证失败，库未处于加载状态");
        return Err(HapticError::DllLoadFailed(
            "DLL 重新加载后验证失败".to_string()
        ));
    }

    // 6. 验证 API 函数是否可用
    let api_arc = get_api().map_err(|e| {
        log::error!("重新加载后获取 API 失败: {}", e);
        HapticError::DllLoadFailed(format!("重新加载后获取 API 失败: {}", e))
    })?;

    let api_guard = api_arc.lock().map_err(|e| {
        log::error!("获取 API 锁失败: {}", e);
        HapticError::ThreadSyncError(format!("获取 API 锁失败: {}", e))
    })?;

    if api_guard.is_none() {
        log::error!("重新加载后 API 实例为空");
        return Err(HapticError::DllLoadFailed(
            "重新加载后 API 实例为空".to_string()
        ));
    }

    log::info!("librtcore 库完全重新加载成功，所有验证通过");
    Ok(())
}


