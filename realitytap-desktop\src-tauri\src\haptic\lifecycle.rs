// RealityTap 核心库生命周期管理
// 确保 awa_realitytap_init 只被调用一次，并提供安全的资源清理机制

use std::sync::{Arc, Mutex, OnceLock, atomic::{AtomicBool, Ordering}};
use std::time::{Duration, Instant};
use crate::haptic::ffi::{get_api, load_library, unload_library, reload_library_completely, HapticActuatorParams, SamplingRateType};
use crate::haptic::error::{HapticResult, HapticError};

/// Send 安全的初始化参数（不包含原始指针）
#[derive(Debug, Clone)]
pub struct SafeHapticParams {
    pub id: u32,
    pub f0: u32,                        // 线性马达实际驱动频率 (Hz)
    pub config_file: String,
    pub sampling_rate: SamplingRateType, // 驱动芯片采样率类型
    pub handler_ptr: usize,             // 使用 usize 存储指针值
}

/// librtcore 初始化状态
#[derive(Debug, Clone, PartialEq)]
pub enum LibraryState {
    /// 未初始化
    NotInitialized,
    /// 正在初始化
    Initializing,
    /// 已初始化
    Initialized,
    /// 正在重新初始化
    Reinitializing,
    /// 正在清理
    Cleaning,
    /// 清理完成
    Cleaned,
    /// 错误状态
    Error(String),
}

/// 初始化统计信息（不包含原始指针）
#[derive(Debug, Clone)]
struct InitializationStats {
    device_count: usize,
    init_time: Instant,
    init_count: u32,
}

/// librtcore 生命周期管理器
pub struct LibraryLifecycleManager {
    /// 当前状态
    state: Arc<Mutex<LibraryState>>,
    /// 是否已经初始化过（原子操作，用于快速检查）
    is_ever_initialized: AtomicBool,
    /// 初始化统计信息
    init_stats: Arc<Mutex<Option<InitializationStats>>>,
    /// 强制清理标志
    force_cleanup_flag: AtomicBool,
}

impl LibraryLifecycleManager {
    /// 创建新的生命周期管理器
    pub fn new() -> Self {
        Self {
            state: Arc::new(Mutex::new(LibraryState::NotInitialized)),
            is_ever_initialized: AtomicBool::new(false),
            init_stats: Arc::new(Mutex::new(None)),
            force_cleanup_flag: AtomicBool::new(false),
        }
    }

    /// 获取当前状态
    pub fn get_state(&self) -> LibraryState {
        self.state.lock().unwrap().clone()
    }

    /// 检查是否已初始化
    pub fn is_initialized(&self) -> bool {
        matches!(self.get_state(), LibraryState::Initialized)
    }

    /// 检查是否曾经初始化过
    pub fn is_ever_initialized(&self) -> bool {
        self.is_ever_initialized.load(Ordering::Acquire)
    }

    /// 安全初始化 librtcore
    /// 确保 awa_realitytap_init 只被调用一次
    pub async fn safe_initialize(&self, params: Vec<SafeHapticParams>) -> HapticResult<()> {
        // 快速检查是否已经初始化
        if self.is_initialized() {
            log::warn!("librtcore 已经初始化，跳过重复初始化");
            return Ok(());
        }

        // 获取状态锁并检查状态（在独立作用域中）
        {
            let mut state_guard = self.state.lock().map_err(|e| {
                HapticError::ThreadSyncError(format!("获取状态锁失败: {}", e))
            })?;

            // 再次检查状态（双重检查锁定模式）
            match *state_guard {
                LibraryState::Initialized => {
                    log::warn!("librtcore 已经初始化（双重检查）");
                    return Ok(());
                }
                LibraryState::Initializing => {
                    return Err(HapticError::InitializationFailed(
                        "librtcore 正在初始化中，请等待".to_string()
                    ));
                }
                LibraryState::Reinitializing => {
                    return Err(HapticError::InitializationFailed(
                        "librtcore 正在重新初始化中，请等待".to_string()
                    ));
                }
                LibraryState::Cleaning => {
                    return Err(HapticError::InitializationFailed(
                        "librtcore 正在清理中，请等待".to_string()
                    ));
                }
                _ => {}
            }

            // 设置初始化状态
            *state_guard = LibraryState::Initializing;
        } // 作用域结束，state_guard 自动释放

        log::info!("开始安全初始化 librtcore，执行器数量: {}", params.len());

        // 执行初始化
        let device_count = params.len();
        let result = self.perform_initialization(params).await;

        // 更新状态
        match result {
            Ok(()) => {
                // 成功时更新状态
                {
                    let mut state_guard = self.state.lock().unwrap();
                    *state_guard = LibraryState::Initialized;
                }
                self.is_ever_initialized.store(true, Ordering::Release);

                // 更新初始化统计
                {
                    let mut stats_guard = self.init_stats.lock().unwrap();
                    *stats_guard = Some(InitializationStats {
                        device_count,
                        init_time: Instant::now(),
                        init_count: stats_guard.as_ref().map(|s| s.init_count + 1).unwrap_or(1),
                    });
                }

                log::info!("librtcore 初始化成功");
                Ok(())
            }
            Err(e) => {
                // 失败时更新状态
                {
                    let mut state_guard = self.state.lock().unwrap();
                    *state_guard = LibraryState::Error(e.to_string());
                }
                log::error!("librtcore 初始化失败: {}", e);
                Err(e)
            }
        }
    }

    /// 执行实际的初始化操作
    async fn perform_initialization(&self, params: Vec<SafeHapticParams>) -> HapticResult<()> {
        // 1. 确保库已加载
        load_library()?;

        // 2. 获取 API 实例
        let api_arc = get_api()?;
        let api_guard = api_arc.lock().map_err(|e| {
            HapticError::ThreadSyncError(format!("获取API锁失败: {}", e))
        })?;

        let api = api_guard.as_ref().ok_or_else(|| {
            HapticError::ApiNotLoaded("API未加载".to_string())
        })?;

        // 3. 转换为 C 结构体并调用 awa_realitytap_init
        let mut c_params: Vec<HapticActuatorParams> = Vec::new();
        let mut config_strings: Vec<std::ffi::CString> = Vec::new();

        for param in &params {
            // 解析配置文件路径为绝对路径（解决 MSI 安装后自动启动的工作目录问题）
            let resolved_config_file = crate::haptic::ffi::resolve_config_file_path(&param.config_file)?;

            // 准备配置文件路径
            let config_file = std::ffi::CString::new(resolved_config_file.clone())
                .map_err(|e| HapticError::InvalidParameter(format!("配置文件路径无效: {}", e)))?;
            config_strings.push(config_file);

            // 创建 C 参数结构体
            let c_param = HapticActuatorParams {
                id: param.id,
                f0: param.f0,
                file: config_strings.last().unwrap().as_ptr(),
                sampling_rate: param.sampling_rate,
                output_handler: param.handler_ptr as *mut std::os::raw::c_void,
            };
            c_params.push(c_param);

            log::debug!("设备 {} 配置文件路径已解析: {} -> {}", param.id, param.config_file, resolved_config_file);
        }

        let params_ptr = c_params.as_ptr();
        let count = c_params.len() as isize;

        let result = unsafe { (api.init)(params_ptr as *mut _, count) };

        if result != 0 {
            return Err(HapticError::InitializationFailed(
                format!("awa_realitytap_init 失败，错误码: {}", result)
            ));
        }

        log::info!("awa_realitytap_init 调用成功");
        Ok(())
    }

    /// 安全清理资源
    /// 由于 librtcore 没有明确的清理 API，我们通过卸载库来强制清理
    pub async fn safe_cleanup(&self) -> HapticResult<()> {
        // 检查和设置状态（在作用域内完成）
        let should_cleanup = {
            let mut state_guard = self.state.lock().map_err(|e| {
                HapticError::ThreadSyncError(format!("获取状态锁失败: {}", e))
            })?;

            // 检查当前状态
            match *state_guard {
                LibraryState::NotInitialized | LibraryState::Cleaned => {
                    log::info!("librtcore 未初始化或已清理，无需清理");
                    return Ok(());
                }
                LibraryState::Cleaning => {
                    return Err(HapticError::CleanupFailed(
                        "librtcore 正在清理中".to_string()
                    ));
                }
                _ => {}
            }

            // 设置清理状态
            *state_guard = LibraryState::Cleaning;
            true
        }; // state_guard 在这里被释放

        if should_cleanup {
            log::info!("开始清理 librtcore 资源");

            // 执行清理
            let result = self.perform_cleanup().await;

            // 更新状态（在新的作用域内）
            {
                let mut state_guard = self.state.lock().unwrap();
                match result {
                    Ok(()) => {
                        *state_guard = LibraryState::Cleaned;
                        log::info!("librtcore 资源清理成功");
                    }
                    Err(ref e) => {
                        *state_guard = LibraryState::Error(e.to_string());
                        log::error!("librtcore 资源清理失败: {}", e);
                    }
                }
            } // state_guard 在这里被释放

            result
        } else {
            Ok(())
        }
    }

    /// 执行实际的清理操作
    async fn perform_cleanup(&self) -> HapticResult<()> {
        use crate::haptic::handler::{start_waiting_for_callback, wait_for_callback_completion, stop_waiting_for_callback};

        // 1. 开始等待回调完成信号
        start_waiting_for_callback();

        // 2. 尝试停止所有振动
        let mut stop_called = false;
        if let Ok(api_arc) = get_api() {
            if let Ok(api_guard) = api_arc.lock() {
                if let Some(api) = api_guard.as_ref() {
                    log::info!("停止所有振动");
                    let result = unsafe { (api.stop_vibration)() };
                    if result != 0 {
                        // 错误码 1 通常表示设备未连接，在清理时这是正常情况
                        if result == 1 {
                            log::debug!("停止振动时设备未连接（清理时正常情况），错误码: {}", result);
                            stop_called = true; // 仍然等待回调，以防有延迟的回调
                        } else {
                            log::warn!("停止振动失败，错误码: {}", result);
                        }
                    } else {
                        stop_called = true;
                    }
                }
            }
        }

        // 3. 如果成功调用了停止操作，等待回调完成
        if stop_called {
            log::info!("等待 librtcore 异步回调完成...");
            let callback_completed = wait_for_callback_completion(1000).await; // 等待最多1秒

            if callback_completed {
                log::info!("librtcore 回调已完成");
            } else {
                log::warn!("等待 librtcore 回调超时，继续清理流程");
            }
        } else {
            log::info!("未调用停止操作，跳过回调等待");
            // 等待一段时间确保操作完成（降级方案）
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        // 4. 停止等待回调
        stop_waiting_for_callback();

        // 5. 卸载动态库（这会强制清理所有资源）
        unload_library()?;

        log::info!("动态库卸载完成");
        Ok(())
    }

    /// 安全重新初始化
    /// 先清理再初始化
    pub async fn safe_reinitialize(&self, params: Vec<SafeHapticParams>) -> HapticResult<()> {
        // 检查和设置状态（在作用域内完成）
        let should_reinitialize = {
            let mut state_guard = self.state.lock().map_err(|e| {
                HapticError::ThreadSyncError(format!("获取状态锁失败: {}", e))
            })?;

            // 检查当前状态
            match *state_guard {
                LibraryState::Reinitializing => {
                    return Err(HapticError::InitializationFailed(
                        "librtcore 正在重新初始化中".to_string()
                    ));
                }
                _ => {}
            }

            // 设置重新初始化状态
            *state_guard = LibraryState::Reinitializing;
            true
        }; // state_guard 在这里被释放

        if should_reinitialize {
            log::info!("开始重新初始化 librtcore");

            // 执行重新初始化
            let result = self.perform_reinitialize(params).await;

            // 更新状态（在新的作用域内）
            {
                let mut state_guard = self.state.lock().unwrap();
                match result {
                    Ok(()) => {
                        *state_guard = LibraryState::Initialized;
                        log::info!("librtcore 重新初始化成功");
                    }
                    Err(ref e) => {
                        *state_guard = LibraryState::Error(e.to_string());
                        log::error!("librtcore 重新初始化失败: {}", e);
                    }
                }
            } // state_guard 在这里被释放

            result
        } else {
            Ok(())
        }
    }

    /// 执行重新初始化
    async fn perform_reinitialize(&self, params: Vec<SafeHapticParams>) -> HapticResult<()> {
        // 由于 awa_realitytap_reinit 存在已知缺陷，直接使用 DLL 重新加载方案
        log::info!("使用 DLL 重新加载方案进行重新初始化（绕过有缺陷的 awa_realitytap_reinit）");

        // 使用 DLL 重新加载进行重新初始化
        match self.perform_dll_reload_reinitialize(&params).await {
            Ok(()) => {
                log::info!("使用 DLL 重新加载重新初始化成功");
                Ok(())
            }
            Err(e) => {
                log::warn!("DLL 重新加载失败，回退到清理+重新初始化模式: {}", e);

                // 回退方案：先清理现有资源
                self.perform_cleanup().await?;

                // 等待一段时间
                tokio::time::sleep(Duration::from_millis(200)).await;

                // 重新初始化
                self.perform_initialization(params).await?;

                Ok(())
            }
        }
    }

    /// 执行直接重新初始化（使用 awa_realitytap_reinit API）
    async fn perform_direct_reinitialize(&self, params: &[SafeHapticParams]) -> HapticResult<()> {
        // 1. 确保库已加载
        load_library()?;

        // 2. 获取 API 实例
        let api_arc = get_api()?;
        let api_guard = api_arc.lock().map_err(|e| {
            HapticError::ThreadSyncError(format!("获取API锁失败: {}", e))
        })?;

        let api = api_guard.as_ref().ok_or_else(|| {
            HapticError::ApiNotLoaded("API未加载".to_string())
        })?;

        // 3. 转换参数为 C 兼容格式
        let mut c_params: Vec<HapticActuatorParams> = Vec::new();
        let mut config_strings: Vec<std::ffi::CString> = Vec::new();

        for param in params {
            // 准备配置文件路径
            let config_file = std::ffi::CString::new(param.config_file.clone())
                .map_err(|e| HapticError::InvalidParameter(format!("配置文件路径无效: {}", e)))?;
            config_strings.push(config_file);

            // 创建 C 参数结构体
            let c_param = HapticActuatorParams {
                id: param.id,
                f0: param.f0,
                file: config_strings.last().unwrap().as_ptr(),
                sampling_rate: param.sampling_rate,
                output_handler: param.handler_ptr as *mut std::os::raw::c_void,
            };
            c_params.push(c_param);
        }

        log::info!("调用 awa_realitytap_reinit，设备数量: {}", c_params.len());

        // 4. 调用 awa_realitytap_reinit
        let params_ptr = c_params.as_ptr();
        let count = c_params.len() as isize;

        let result = unsafe { (api.reinit)(params_ptr as *mut _, count) };

        if result != 0 {
            return Err(HapticError::InitializationFailed(
                format!("awa_realitytap_reinit 失败，错误码: {}", result)
            ));
        }

        log::info!("awa_realitytap_reinit 调用成功");
        Ok(())
    }

    /// 执行基于 DLL 重新加载的重新初始化
    ///
    /// 这个方法会：
    /// 1. 完全重新加载 librtcore.dll
    /// 2. 使用保持的 Handler 重新初始化
    /// 3. 确保所有内部状态被重置
    async fn perform_dll_reload_reinitialize(&self, params: &[SafeHapticParams]) -> HapticResult<()> {
        log::info!("开始基于 DLL 重新加载的重新初始化");

        // 1. 完全重新加载 DLL
        log::info!("完全重新加载 librtcore.dll");
        reload_library_completely()?;

        // 2. 获取重新加载后的 API 实例
        let api_arc = get_api()?;
        let api_guard = api_arc.lock().map_err(|e| {
            HapticError::ThreadSyncError(format!("获取API锁失败: {}", e))
        })?;

        let api = api_guard.as_ref().ok_or_else(|| {
            HapticError::ApiNotLoaded("DLL 重新加载后 API 未加载".to_string())
        })?;

        // 3. 转换参数为 C 兼容格式（与原有逻辑相同）
        let mut c_params: Vec<HapticActuatorParams> = Vec::new();
        let mut config_strings: Vec<std::ffi::CString> = Vec::new();

        for param in params {
            // 解析配置文件路径为绝对路径（解决 MSI 安装后自动启动的工作目录问题）
            let resolved_config_file = crate::haptic::ffi::resolve_config_file_path(&param.config_file)?;

            // 准备配置文件路径
            let config_file = std::ffi::CString::new(resolved_config_file.clone())
                .map_err(|e| HapticError::InvalidParameter(format!("配置文件路径无效: {}", e)))?;
            config_strings.push(config_file);

            // 创建 C 参数结构体，使用保持的 Handler 指针
            let c_param = HapticActuatorParams {
                id: param.id,
                f0: param.f0,
                file: config_strings.last().unwrap().as_ptr(),
                sampling_rate: param.sampling_rate,
                output_handler: param.handler_ptr as *mut std::os::raw::c_void,
            };
            c_params.push(c_param);

            log::debug!("DLL 重新加载后设备 {} 配置文件路径已解析: {} -> {}", param.id, param.config_file, resolved_config_file);
        }

        log::info!("调用 awa_realitytap_init（DLL 重新加载后），设备数量: {}", c_params.len());

        // 4. 调用 awa_realitytap_init（不是 reinit，因为 DLL 已重新加载）
        let params_ptr = c_params.as_ptr();
        let count = c_params.len() as isize;

        let result = unsafe { (api.init)(params_ptr as *mut _, count) };

        if result != 0 {
            return Err(HapticError::InitializationFailed(
                format!("DLL 重新加载后 awa_realitytap_init 失败，错误码: {}", result)
            ));
        }

        log::info!("DLL 重新加载后 awa_realitytap_init 调用成功");
        Ok(())
    }

    /// 只清理库资源，不影响 outputHandler 单例
    pub async fn cleanup_library_only(&self) -> HapticResult<()> {
        log::info!("开始清理库资源（保留单例）");

        // 检查状态并决定是否需要清理
        let should_cleanup = {
            let state_guard = self.state.lock().unwrap();
            match *state_guard {
                LibraryState::NotInitialized => {
                    log::info!("库未初始化，无需清理");
                    return Ok(());
                }
                LibraryState::Initializing | LibraryState::Reinitializing => {
                    log::warn!("库正在初始化中，等待完成后清理");
                    false // 需要等待
                }
                LibraryState::Cleaning => {
                    log::info!("库正在清理中，无需重复清理");
                    return Ok(());
                }
                LibraryState::Cleaned => {
                    log::info!("库已清理完成，无需重复清理");
                    return Ok(());
                }
                LibraryState::Initialized | LibraryState::Error(_) => {
                    log::info!("开始清理库");
                    true // 可以清理
                }
            }
        };

        // 如果需要等待，递归调用
        if !should_cleanup {
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            return Box::pin(self.cleanup_library_only()).await;
        }

        // 设置为清理状态
        {
            let mut state_guard = self.state.lock().unwrap();
            *state_guard = LibraryState::NotInitialized;
        }

        // 执行库清理（简化版，不等待回调）
        self.perform_library_cleanup_only().await?;

        log::info!("库资源清理完成");
        Ok(())
    }

    /// 强制重置（用于错误恢复）
    pub async fn force_reset(&self) -> HapticResult<()> {
        log::warn!("执行强制重置");

        // 设置强制清理标志
        self.force_cleanup_flag.store(true, Ordering::Release);

        // 强制卸载库
        let _ = unload_library();

        // 重置状态
        let mut state_guard = self.state.lock().unwrap();
        *state_guard = LibraryState::NotInitialized;

        // 清除统计信息
        let mut stats_guard = self.init_stats.lock().unwrap();
        *stats_guard = None;

        // 重置标志
        self.is_ever_initialized.store(false, Ordering::Release);
        self.force_cleanup_flag.store(false, Ordering::Release);

        log::info!("强制重置完成");
        Ok(())
    }

    /// 执行简化的库清理（不等待回调完成）
    async fn perform_library_cleanup_only(&self) -> HapticResult<()> {
        log::info!("执行简化库清理");

        // 1. 尝试停止所有振动
        if let Ok(api_arc) = get_api() {
            if let Ok(api_guard) = api_arc.lock() {
                if let Some(api) = api_guard.as_ref() {
                    log::info!("停止所有振动");
                    let _ = unsafe { (api.stop_vibration)() };
                }
            }
        }

        // 2. 停止所有振动（作为清理的一部分）
        if let Ok(api_arc) = get_api() {
            if let Ok(api_guard) = api_arc.lock() {
                if let Some(api) = api_guard.as_ref() {
                    log::info!("确保所有振动已停止");
                    let _ = unsafe { (api.stop_vibration)() };
                }
            }
        }

        // 3. 卸载动态库
        unload_library()?;

        log::info!("简化库清理完成");
        Ok(())
    }
}

// 全局生命周期管理器实例
static LIFECYCLE_MANAGER: OnceLock<LibraryLifecycleManager> = OnceLock::new();

/// 获取全局生命周期管理器实例
pub fn get_lifecycle_manager() -> &'static LibraryLifecycleManager {
    LIFECYCLE_MANAGER.get_or_init(|| LibraryLifecycleManager::new())
}

/// 便捷函数：安全初始化
pub async fn safe_init_librtcore(params: Vec<SafeHapticParams>) -> HapticResult<()> {
    get_lifecycle_manager().safe_initialize(params).await
}

/// 便捷函数：安全清理
pub async fn safe_cleanup_librtcore() -> HapticResult<()> {
    get_lifecycle_manager().safe_cleanup().await
}


