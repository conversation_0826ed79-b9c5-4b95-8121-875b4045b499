import type { Ref } from "vue";
import type { ScrollbarInst } from "naive-ui";
import type { RenderableEvent } from "@/types/haptic-editor";
import { WAVEFORM_SAFE_OFFSET } from "../utils/drawing-helpers";
import { LAYOUT_CONSTANTS } from "../config/waveform-constants";

// 使用 ReturnType 来获取正确的 Store 类型
type FileWaveformEditorStore = ReturnType<typeof import("@/stores/haptics-editor-store").useFileWaveformEditorStore>;

/**
 * 波形图配置管理 Composable
 * 统一管理所有 composable 的配置对象，减少主组件的复杂度
 */

// 基础配置接口
export interface BaseWaveformConfig {
  padding: { top: number; right: number; bottom: number; left: number };
  safeOffset: number;
}

// 画布配置
export interface CanvasConfigOptions {
  availableParentWidth: number;
  totalEffectDuration: number;
  baselineDuration: number;
  audioDuration?: number | null;
}

// 坐标系统配置
export interface CoordinateSystemConfig {
  canvasWidth: Ref<number>;
  canvasHeight: Ref<number>;
  virtualScrollOffset: Ref<number>;
  getEffectiveDuration: () => number;
  getGraphAreaWidth: () => number;
  getLogicalGraphAreaWidth: () => number;
  getGraphAreaHeight: () => number;
}

// 事件绘制配置
export interface EventDrawingConfigOptions {
  mapTimeToXLocal: (time: number) => number;
  mapIntensityToYLocal: (intensity: number) => number;
  getGraphAreaWidth: () => number;
  isEventSelected: (eventId: string) => boolean;
  virtualScrollOffset: Ref<number>;
  waveformStore: FileWaveformEditorStore;
}

// 拖拽配置
export interface DragConfigOptions {
  canvas: Ref<HTMLCanvasElement | null>;
  events: () => RenderableEvent[];
  waveformStore: FileWaveformEditorStore;
  mapTimeToXLocal: (time: number) => number;
  mapIntensityToYLocal: (intensity: number) => number;
  mapYToIntensityLocal: (y: number) => number;
  mapXOffsetToTimeOffsetLocal: (xOffset: number) => number;
  mapXToTimeOffsetLocal: (x: number) => number;
  smartDrawWaveform: (forceRedraw?: boolean) => void;
  invalidateEventCache: () => void;
  isFrequencyAdjustmentKeyPressed: Ref<boolean>;
  DRAG_THRESHOLD_MS: number;
  audioDuration?: number | null;
  setDragEndTime: (time: number) => void;
  showEventTooltip: (x: number, y: number) => void;
  hideEventTooltip: () => void;
  updateEventTooltipPosition: (x: number, y: number) => void;
}

// 自动滚动配置
export interface AutoScrollConfigOptions {
  horizontalScrollbarRef: Ref<ScrollbarInst | null>;
  availableParentWidth: number;
  PADDING: { top: number; right: number; bottom: number; left: number };
  logicalCanvasWidth: Ref<number>;
  virtualScrollOffset: Ref<number>;
  scrollLeftValue: Ref<number>;
  getEffectiveDuration: () => number;
  getLogicalGraphAreaWidth: () => number;
}

export function useWaveformConfig() {
  // 基础配置 - 使用统一的常量配置确保一致性
  const createBaseConfig = (): BaseWaveformConfig => ({
    padding: LAYOUT_CONSTANTS.DEFAULT_PADDING, // 使用常量文件中的默认padding配置
    safeOffset: WAVEFORM_SAFE_OFFSET,
  });

  // 创建画布配置
  const createCanvasConfig = (options: CanvasConfigOptions) => ({
    padding: createBaseConfig().padding,
    safeOffset: WAVEFORM_SAFE_OFFSET,
    ...options,
  });

  // 创建坐标系统配置
  const createCoordinateConfig = (
    canvasState: CoordinateSystemConfig,
    baseConfig: BaseWaveformConfig
  ) => ({
    ...canvasState,
    padding: baseConfig.padding,
  });

  // 创建事件绘制配置
  const createEventDrawingConfig = (options: EventDrawingConfigOptions) => ({
    ...options,
  });

  // 创建拖拽配置
  const createDragConfig = (options: DragConfigOptions) => ({
    ...options,
  });

  // 创建自动滚动配置
  const createAutoScrollConfig = (options: AutoScrollConfigOptions) => ({
    ...options,
  });

  return {
    createBaseConfig,
    createCanvasConfig,
    createCoordinateConfig,
    createEventDrawingConfig,
    createDragConfig,
    createAutoScrollConfig,
  };
}
