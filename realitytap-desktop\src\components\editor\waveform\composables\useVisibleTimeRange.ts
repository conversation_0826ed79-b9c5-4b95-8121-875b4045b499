// 可见时间范围计算组合式函数
// 负责计算当前画布中可见的时间范围（开始时间和结束时间）
// 支持虚拟滚动和缩放功能

import { computed, readonly, type Ref } from "vue";

/**
 * 可见时间范围计算配置接口
 */
export interface VisibleTimeRangeConfig {
  // 虚拟滚动偏移量
  virtualScrollOffset: Ref<number>;

  // 当前缩放级别
  currentZoomLevel: Ref<number>;

  // 画布尺寸获取函数
  getLogicalGraphAreaWidth: () => number;
  getGraphAreaWidth: () => number;

  // 有效时长获取函数
  getEffectiveDuration: () => number;

  // 父容器可用宽度获取函数
  getAvailableParentWidth: () => number;

  // 调试模式（可选）
  debugMode?: boolean;
}

/**
 * 可见时间范围结果接口
 */
export interface VisibleTimeRangeResult {
  // 可见开始时间（毫秒）
  visibleStartTime: Readonly<Ref<number>>;
  
  // 可见结束时间（毫秒）
  visibleEndTime: Readonly<Ref<number>>;
  
  // 可见时间长度（毫秒）
  visibleDuration: Readonly<Ref<number>>;
  
  // 可见时间范围的相对位置（0-1）
  visibleStartRatio: Readonly<Ref<number>>;
  visibleEndRatio: Readonly<Ref<number>>;
  
  // 工具函数
  isTimeVisible: (time: number) => boolean;
  getVisibleTimeRange: () => { startTime: number; endTime: number };
}

/**
 * 可见时间范围计算 Composable
 * 
 * 基于虚拟滚动偏移量、缩放级别和画布尺寸计算当前可见的时间范围
 * 
 * @param config 配置参数
 * @returns 可见时间范围相关的响应式数据和工具函数
 */
export function useVisibleTimeRange(config: VisibleTimeRangeConfig): VisibleTimeRangeResult {
  const {
    virtualScrollOffset,
    currentZoomLevel,
    getLogicalGraphAreaWidth,
    getGraphAreaWidth,
    getEffectiveDuration,
    getAvailableParentWidth,
    debugMode = false
  } = config;

  /**
   * 计算可见开始时间
   */
  const visibleStartTime = computed(() => {
    try {
      const logicalGraphWidth = getLogicalGraphAreaWidth();
      const zoomLevel = currentZoomLevel.value;
      const effectiveDuration = getEffectiveDuration();
      const currentVirtualOffset = virtualScrollOffset.value;

      // 防止除零错误
      if (logicalGraphWidth <= 0 || effectiveDuration <= 0 || zoomLevel <= 0) {
        if (debugMode) {
          console.warn('[useVisibleTimeRange] Invalid parameters for startTime calculation', {
            logicalGraphWidth,
            effectiveDuration,
            zoomLevel
          });
        }
        return 0;
      }

      // 应用缩放：缩放后的逻辑宽度
      const scaledLogicalWidth = logicalGraphWidth * zoomLevel;
      
      // 计算可见开始时间
      const startTime = (currentVirtualOffset / scaledLogicalWidth) * effectiveDuration;
      
      // 确保时间在有效范围内
      const clampedStartTime = Math.max(0, Math.min(startTime, effectiveDuration));
      
      if (debugMode) {
        console.debug('[useVisibleTimeRange] Calculated startTime', {
          currentVirtualOffset,
          logicalGraphWidth,
          zoomLevel,
          scaledLogicalWidth,
          effectiveDuration,
          startTime,
          clampedStartTime
        });
      }
      
      return clampedStartTime;
    } catch (error) {
      console.error('[useVisibleTimeRange] Error calculating startTime:', error);
      return 0;
    }
  });

  /**
   * 计算可见结束时间
   */
  const visibleEndTime = computed(() => {
    try {
      const logicalGraphWidth = getLogicalGraphAreaWidth();
      const physicalGraphWidth = getGraphAreaWidth();
      const zoomLevel = currentZoomLevel.value;
      const effectiveDuration = getEffectiveDuration();
      const currentVirtualOffset = virtualScrollOffset.value;

      // 防止除零错误
      if (logicalGraphWidth <= 0 || effectiveDuration <= 0 || zoomLevel <= 0) {
        if (debugMode) {
          console.warn('[useVisibleTimeRange] Invalid parameters for endTime calculation', {
            logicalGraphWidth,
            physicalGraphWidth,
            effectiveDuration,
            zoomLevel
          });
        }
        return effectiveDuration;
      }

      // 应用缩放：缩放后的逻辑宽度
      const scaledLogicalWidth = logicalGraphWidth * zoomLevel;

      // 计算可见结束时间 - 使用父容器的实际可见宽度
      const availableParentWidth = getAvailableParentWidth();
      const parentGraphWidth = Math.max(0, availableParentWidth - 130); // 减去左右padding
      const visibleWidth = Math.min(parentGraphWidth, logicalGraphWidth);
      const endTime = ((currentVirtualOffset + visibleWidth) / scaledLogicalWidth) * effectiveDuration;

      // 确保时间在有效范围内
      const clampedEndTime = Math.max(0, Math.min(endTime, effectiveDuration));

      if (debugMode) {
        console.debug('[useVisibleTimeRange] Calculated endTime', {
          currentVirtualOffset,
          availableParentWidth,
          visibleWidth,
          scaledLogicalWidth,
          effectiveDuration,
          endTime,
          clampedEndTime
        });
      }

      return clampedEndTime;
    } catch (error) {
      console.error('[useVisibleTimeRange] Error calculating endTime:', error);
      return getEffectiveDuration();
    }
  });

  /**
   * 计算可见时间长度
   */
  const visibleDuration = computed(() => {
    const duration = visibleEndTime.value - visibleStartTime.value;
    return Math.max(0, duration);
  });

  /**
   * 计算可见开始时间的相对位置（0-1）
   */
  const visibleStartRatio = computed(() => {
    const effectiveDuration = getEffectiveDuration();
    if (effectiveDuration <= 0) return 0;
    return visibleStartTime.value / effectiveDuration;
  });

  /**
   * 计算可见结束时间的相对位置（0-1）
   */
  const visibleEndRatio = computed(() => {
    const effectiveDuration = getEffectiveDuration();
    if (effectiveDuration <= 0) return 1;
    return visibleEndTime.value / effectiveDuration;
  });

  /**
   * 判断指定时间是否在可见范围内
   */
  const isTimeVisible = (time: number): boolean => {
    return time >= visibleStartTime.value && time <= visibleEndTime.value;
  };

  /**
   * 获取当前可见时间范围
   */
  const getVisibleTimeRange = () => {
    return {
      startTime: visibleStartTime.value,
      endTime: visibleEndTime.value
    };
  };

  return {
    // 响应式数据
    visibleStartTime: readonly(visibleStartTime),
    visibleEndTime: readonly(visibleEndTime),
    visibleDuration: readonly(visibleDuration),
    visibleStartRatio: readonly(visibleStartRatio),
    visibleEndRatio: readonly(visibleEndRatio),
    
    // 工具函数
    isTimeVisible,
    getVisibleTimeRange
  };
}

/**
 * 创建可见时间范围计算器的工厂函数
 * 用于在不同组件中创建独立的时间范围计算实例
 */
export function createVisibleTimeRangeCalculator(config: VisibleTimeRangeConfig) {
  return useVisibleTimeRange(config);
}
