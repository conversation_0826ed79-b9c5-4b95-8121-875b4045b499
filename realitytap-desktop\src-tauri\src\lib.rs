// Declare modules
mod commands;
mod error;
mod models;
mod project;
mod audio;
mod utils;
mod device;
mod haptic;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    use tauri::Manager;

    tauri::Builder::default()
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_updater::Builder::new().build()) // Re-enabled for OTA functionality
        .plugin(tauri_plugin_process::init())
        .plugin(tauri_plugin_os::init())
        .setup(|app| {
            // 设置工作目录为可执行文件所在目录（解决 MSI 安装后自动启动的工作目录问题）
            if let Ok(exe_path) = std::env::current_exe() {
                if let Some(exe_dir) = exe_path.parent() {
                    match std::env::set_current_dir(exe_dir) {
                        Ok(()) => {
                            println!("Working directory set to: {}", exe_dir.display());
                        }
                        Err(e) => {
                            eprintln!("Failed to set working directory to {}: {}", exe_dir.display(), e);
                        }
                    }
                } else {
                    eprintln!("Failed to get executable directory");
                }
            } else {
                eprintln!("Failed to get executable path");
            }

            // Get custom log directory using our app_data function
            let log_dir = match commands::app_data::get_app_data_dir() {
                Ok(app_data_dir) => {
                    let log_dir = app_data_dir.join("logs");
                    // Ensure the logs directory exists
                    if let Err(e) = std::fs::create_dir_all(&log_dir) {
                        eprintln!("Failed to create logs directory: {}", e);
                        None
                    } else {
                        Some(log_dir)
                    }
                }
                Err(e) => {
                    eprintln!("Failed to get app data directory: {}", e);
                    None
                }
            };

            // Enable logging with file output for production debugging
            // Note: Clear default targets to avoid duplicate logs
            let mut log_builder = tauri_plugin_log::Builder::default()
                .level(if cfg!(debug_assertions) {
                    log::LevelFilter::Debug
                } else {
                    log::LevelFilter::Info
                })
                .clear_targets() // Clear default targets to prevent duplicate logs
                // Always enable stdout logging
                .target(tauri_plugin_log::Target::new(
                    tauri_plugin_log::TargetKind::Stdout,
                ));

            // Add file logging if we have a valid log directory
            if let Some(log_dir_path) = log_dir {
                log_builder = log_builder.target(tauri_plugin_log::Target::new(
                    tauri_plugin_log::TargetKind::Folder {
                        path: log_dir_path,
                        file_name: Some("realitytap-studio".to_string())
                    },
                ));
            }

            app.handle().plugin(log_builder.build())?;

            log::info!("AWA RealityTap Studio starting up...");

            // Initialize temporary file manager
            log::info!("Initializing temporary file manager...");
            if let Err(e) = utils::temp_file::init_temp_manager() {
                log::warn!("Failed to initialize temp file manager: {}", e);
            } else {
                log::info!("Temporary file manager initialized successfully");
            }

            // Initialize device manager state
            log::info!("Initializing device manager...");
            let device_manager = device::commands::init_device_manager();
            app.manage(device_manager);
            log::info!("Device manager initialized successfully");

            // Initialize global haptic handler singleton
            log::info!("Initializing global haptic handler...");
            haptic::handler::initialize_global_handler(app.handle().clone());
            log::info!("Global haptic handler initialized successfully");

            log::info!("Setup completed successfully");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // App data and settings commands
            commands::get_recent_projects,
            commands::clear_recent_projects,
            commands::remove_recent_project,
            commands::get_window_settings,
            commands::save_window_settings_command,
            // App configuration commands
            commands::get_app_config,
            commands::save_app_config_command,
            commands::reset_app_config,
            commands::create_app_config_backup,
            commands::update_play_effect_dialog_config,
            commands::update_last_selected_motor,
            commands::update_last_selected_sampling_rate,
            // Project-level commands
            commands::load_project,
            commands::save_project,
            commands::create_new_project,
            commands::rename_project,
            // File management commands
            commands::add_file_to_project,
            commands::remove_file_from_project,
            commands::create_he_file_in_project,
            commands::read_file_content_command,
            commands::write_file_content_command,
            commands::rename_file_command,
            commands::show_save_file_dialog,
            // Group management commands
            commands::create_group,
            commands::rename_group_command,
            commands::remove_group_command,
            // Utility commands
            commands::generate_project_from_directory_command,
            commands::refresh_project_directory_command,
            commands::check_file_exists_command,
            commands::move_file_command,
            commands::move_group_command,
            // Version information commands
            commands::get_app_version_info,
            // Process management commands
            commands::list_realitytap_processes,
            commands::close_process_gracefully,
            commands::force_close_process,
            commands::wait_for_processes_exit,
            commands::get_current_app_pid,
            commands::is_process_running,
            commands::get_process_info,
            commands::close_processes_batch,
            commands::exit_application_gracefully,
            // OTA configuration commands
            commands::get_ota_config,
            commands::save_ota_config,
            commands::get_custom_ota_servers,
            commands::save_custom_ota_servers,
            commands::test_ota_server_connection,
            commands::log_update_server_info,
            commands::log_ota_operation,
            // Debug and logging commands
            commands::get_log_file_path,
            commands::read_log_file,
            commands::clear_log_file,

            commands::get_debug_mode,
            commands::set_debug_mode,
            commands::export_debug_info,
            commands::cleanup_temp_files,
            commands::open_external_url,
            // Audio processing commands
            audio::get_audio_info,
            audio::get_audio_info_from_file,
            audio::copy_audio_file_to_project,
            audio::copy_video_file_to_project,
            audio::get_video_audio_info,
            audio::get_audio_amplitude_data,
            audio::get_audio_frequency_data,
            audio::get_audio_analysis_data,
            // Device management commands
            device::scan_devices,
            device::get_scan_status,
            device::get_last_scan_time,
            device::connect_device,
            device::disconnect_device,
            device::get_all_devices,
            device::get_device,
            device::add_device,
            device::remove_device,
            device::update_device,
            device::set_default_device,
            device::get_default_device,
            device::get_device_statistics,
            device::get_device_status,
            device::send_data_to_device,
            device::get_device_manager_config,
            device::update_device_manager_config,
            device::load_device_manager_data,
            device::save_device_manager_data,
            // Haptic feedback commands
            haptic::haptic_init_unified,
            haptic::haptic_start_unified,
            haptic::haptic_stop_unified,
            haptic::haptic_play_unified,
            haptic::haptic_save_to_file,
            haptic::haptic_set_amplitude_unified,
            haptic::haptic_get_status_unified,
            haptic::haptic_cleanup_unified,
            haptic::haptic_reinit_unified,
            haptic::haptic_get_architecture_info,
            // 兼容性命令（保留部分常用的状态查询命令）
            haptic::haptic_get_device_status,
            haptic::haptic_get_all_device_status,
            haptic::haptic_get_library_status,
            haptic::haptic_get_available_configs,
            haptic::haptic_validate_config_file,
            haptic::haptic_get_config_file_info,
            haptic::haptic_get_all_config_info,
            haptic::haptic_force_reset,
            haptic::haptic_get_lifecycle_status,
        ])
        .run(tauri::generate_context!())
        .expect("Run AWA RealityTap Studio error");
}
