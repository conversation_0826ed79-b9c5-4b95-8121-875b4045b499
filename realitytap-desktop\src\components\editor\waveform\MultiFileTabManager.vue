<template>
  <div class="enhanced-multi-file-waveform-manager">
    <!-- Chrome风格多标签页编辑器 -->
    <div v-if="tabManager.tabCount.value > 0" class="tabs-container">
      <!-- 标签页导航栏（可选显示） -->
      <div v-if="showTabBar" class="tab-navigation">
        <div class="tab-list">
          <div v-for="tab in tabManager.tabList.value" :key="tab.fileUuid" :class="['tab-item', { active: tab.isActive, modified: tab.isModified }]">
            <span class="tab-title">{{ tab.fileName }}</span>
            <span v-if="tab.isModified" class="modified-indicator">●</span>
          </div>
        </div>
      </div>

      <!-- 标签页内容区域 -->
      <div class="tab-content-area">
        <div v-for="tab in tabManager.tabList.value" :key="tab.fileUuid" v-show="tab.isActive" class="tab-content">
          <!-- 标签页内容：波形编辑器 + 事件调节面板 -->
          <div class="tab-file-container">
            <!-- 波形编辑器区域 -->
            <div class="waveform-editor-area">
              <WaveformEditor
                :key="`enhanced-editor-${tab.fileUuid}`"
                :file-uuid="tab.fileUuid"
                :effect="tab.effect"
                :audio-duration="tab.editorState.audioDuration"
                @increase-duration="(duration) => handleIncreaseDuration(tab.fileUuid, duration)"
                @close-tab="() => handleTabClose(tab.fileUuid)"
              />
            </div>

            <!-- 视觉分隔线 -->
            <div class="visual-separator"></div>

            <!-- 事件调节面板区域 -->
            <div class="event-adjust-area">
              <EventAdjustPanel :key="`enhanced-adjust-${tab.fileUuid}`" :file-uuid="tab.fileUuid" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <n-empty :description="t('editor.waveform.noFileSelected')" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, onBeforeUnmount, defineProps, defineEmits, computed } from "vue";
import { NEmpty } from "naive-ui";
import WaveformEditor from "./HapticWaveformEditor.vue";
import EventAdjustPanel from "../adjustments/HapticEventPropertiesPanel.vue";
import type { RealityTapEffect } from "@/types/haptic-file";
import { useEnhancedTabManager, type TabManagerConfig } from "@/composables/useEnhancedTabManager";
import { useProjectStore } from "@/stores/haptics-project-store";
import { useI18n } from "@/composables/useI18n";
import { logger, LogModule } from "@/utils/logger/logger";

// 音频振幅数据接口已移动到 Store 中

const props = withDefaults(defineProps<{
  activeFileUuid: string | null; // 当前活跃的文件 UUID
  activeEffect: RealityTapEffect | null; // 当前活跃文件的效果数据
  audioDuration?: number | null; // 音频时长（ms）
  showTabBar?: boolean; // 是否显示标签页导航栏
  maxTabs?: number; // 最大标签页数量
  enableLRU?: boolean; // 是否启用LRU缓存策略
}>(), {
  showTabBar: true,
  maxTabs: 10,
  enableLRU: true
});

const emit = defineEmits<{
  (e: "increase-duration", durationInMs: number): void;
  (e: "tab-switch", fileUuid: string): void;
  (e: "tab-close", fileUuid: string): void;
}>();

// 标签页管理器配置
const tabManagerConfig: TabManagerConfig = {
  maxTabs: props.maxTabs || 10,
  enableLRU: props.enableLRU ?? true,
  autoSave: true,
  debugMode: true, // 开发阶段启用调试
};

// 使用增强型标签页管理器
const tabManager = useEnhancedTabManager(tabManagerConfig);

// 国际化
const { t } = useI18n();
const projectStore = useProjectStore();

// 标签页导航栏显示控制
const showTabBar = computed(() => props.showTabBar ?? false);

// 标签页关闭处理
const handleTabClose = (fileUuid: string) => {
  logger.info(LogModule.PROJECT, `🗂️ ProjectFileManager: 开始关闭标签页 ${fileUuid}`);

  if (tabManager.closeTab(fileUuid)) {
    logger.info(LogModule.PROJECT, `✅ ProjectFileManager: 标签页 ${fileUuid} 关闭成功`);
    emit("tab-close", fileUuid);

    // 如果没有标签页了，通知父组件
    if (tabManager.tabCount.value === 0) {
      logger.info(LogModule.PROJECT, `📭 ProjectFileManager: 所有标签页已关闭`);
      emit("tab-switch", "");
    } else if (tabManager.activeTabKey.value) {
      // 切换到新的活跃标签页
      logger.info(LogModule.PROJECT, `🔄 ProjectFileManager: 切换到活跃标签页 ${tabManager.activeTabKey.value}`);
      emit("tab-switch", tabManager.activeTabKey.value);
    }
  } else {
    logger.warn(LogModule.PROJECT, `❌ ProjectFileManager: 标签页 ${fileUuid} 关闭失败`);
  }
};

// 事件处理器 - 增加时长
const handleIncreaseDuration = (fileUuid: string, durationInMs: number) => {
  // 只有当前活跃标签页的事件才传递给父组件
  if (fileUuid === tabManager.activeTabKey.value) {
    emit("increase-duration", durationInMs);
  }
};

// 音频切换功能已移除：现在根据音频数据自动显示

// 监听活跃文件变化，智能管理标签页
watch(
  [() => props.activeFileUuid, () => props.activeEffect, () => props.audioDuration],
  ([newFileUuid, newEffect, newAudioDuration]) => {
    if (!newFileUuid || !newEffect) {
      // 当没有活跃文件时，关闭所有标签页以确保UI状态一致
      if (tabManager.tabCount.value > 0) {
        logger.info(LogModule.PROJECT, "没有活跃文件，关闭所有标签页");
        tabManager.closeAllTabs();
      }
      return;
    }

    // 使用增强型标签页管理器打开或切换标签页（音频数据现在由 Store 管理）
    // 不传递 audioAmplitudeData，让 Store 保持现有的音频数据
    tabManager.openTab(newFileUuid, newEffect, undefined, newAudioDuration || null);
  },
  { immediate: true, deep: true }
);

// 音频波形显示状态监听已移除：现在根据音频数据自动显示

// 监听项目文件变化，检测文件删除并自动关闭对应标签页
watch(
  () => projectStore.activeProject?.files,
  (newFiles, oldFiles) => {
    if (!oldFiles || !newFiles) return;

    // 检测被删除的文件
    const oldFileUuids = new Set(oldFiles.map(f => f.fileUuid));
    const newFileUuids = new Set(newFiles.map(f => f.fileUuid));

    for (const oldUuid of oldFileUuids) {
      if (!newFileUuids.has(oldUuid)) {
        // 文件被删除，关闭对应的标签页
        if (tabManager.hasTab && tabManager.hasTab(oldUuid)) {
          logger.info(LogModule.PROJECT, `检测到文件删除，关闭标签页: ${oldUuid}`);
          tabManager.closeTab(oldUuid);
        }
      }
    }
  },
  { deep: true }
);

// 组件卸载时清理所有标签页
onBeforeUnmount(() => {
  logger.info(LogModule.PROJECT, "EnhancedMultiFileWaveformManager: 组件卸载，清理所有标签页");
  tabManager.closeAllTabs();
});

// 暴露标签页管理器给父组件
defineExpose({
  tabManager,
  openTab: tabManager.openTab,
  switchToTab: tabManager.switchToTab,
  closeTab: tabManager.closeTab,
  closeAllTabs: tabManager.closeAllTabs,
  getTabCount: () => tabManager.tabCount.value,
  getActiveTab: () => tabManager.activeTab.value,
});
</script>

<style scoped>
.enhanced-multi-file-waveform-manager {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 标签页容器 */
.tabs-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 标签页导航栏 */
.tab-navigation {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
  padding: 0;
}

.tab-list {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-list::-webkit-scrollbar {
  display: none;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
  min-width: 120px;
  max-width: 200px;
  position: relative;
}

.tab-item.active {
  background: rgba(54, 173, 106, 0.2);
  border-bottom: 2px solid #36ad6a;
}

.tab-item.modified .tab-title {
  font-style: italic;
}

.tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
}

.modified-indicator {
  color: #f0a020;
  margin-left: 4px;
  font-size: 14px;
}

/* 标签页内容区域 */
.tab-content-area {
  flex: 1;
  height: 0;
  position: relative;
}

.tab-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 标签页内容容器 */
.tab-file-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
}

.waveform-editor-area {
  flex: 1;
  height: 100%;
  min-width: 0;
}

/* 视觉分隔线样式 */
.visual-separator {
  width: 1px;
  height: 100%;
  background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.08) 10%, rgba(255, 255, 255, 0.12) 50%, rgba(255, 255, 255, 0.08) 90%, transparent 100%);
  box-shadow: 1px 0 0 rgba(255, 255, 255, 0.02), -1px 0 0 rgba(0, 0, 0, 0.1);
  position: relative;
}

.visual-separator::before {
  content: "";
  position: absolute;
  top: 0;
  left: -1px;
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom, transparent 0%, rgba(54, 173, 106, 0.03) 20%, rgba(54, 173, 106, 0.05) 50%, rgba(54, 173, 106, 0.03) 80%, transparent 100%);
  pointer-events: none;
}

.event-adjust-area {
  width: 260px;
  min-width: 260px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-state {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
