// Version information commands
use crate::error::Result;
use serde::{Deserialize, Serialize};
use std::fs;
use tauri;

/// Application version information
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct AppVersionInfo {
    /// Application name
    pub app_name: String,
    /// Application version from package.json
    pub app_version: String,
    /// Rust backend version from Cargo.toml
    pub backend_version: String,
    /// Build date (compile time)
    pub build_date: String,
    /// Build environment (debug/release)
    pub build_mode: String,
    /// Target architecture
    pub target_arch: String,
    /// Target OS
    pub target_os: String,
}

/// Get application version information
#[tauri::command]
pub async fn get_app_version_info() -> Result<AppVersionInfo> {
    log::info!("Getting application version information");

    // Get package.json version with fallback
    let app_version = match get_package_version() {
        Ok(version) => {
            log::info!("Successfully read app version from package.json: {}", version);
            version
        }
        Err(e) => {
            log::warn!("Failed to read package.json version: {}, using Cargo version as fallback", e);
            env!("CARGO_PKG_VERSION").to_string()
        }
    };

    // Get Cargo.toml version
    let backend_version = env!("CARGO_PKG_VERSION").to_string();
    log::info!("Backend version from Cargo.toml: {}", backend_version);

    // Build date (compile time)
    let build_date = get_build_date();
    log::info!("Build date: {}", build_date);

    // Build mode
    let build_mode = if cfg!(debug_assertions) {
        "debug".to_string()
    } else {
        "release".to_string()
    };

    // Target information with compile-time and runtime fallbacks
    let target_arch = option_env!("TARGET_ARCH")
        .map(|s| s.to_string())
        .or_else(|| std::env::var("CARGO_CFG_TARGET_ARCH").ok())
        .unwrap_or_else(|| "unknown".to_string());
    let target_os = option_env!("TARGET_OS")
        .map(|s| s.to_string())
        .or_else(|| std::env::var("CARGO_CFG_TARGET_OS").ok())
        .unwrap_or_else(|| "unknown".to_string());

    let version_info = AppVersionInfo {
        app_name: "RealityTap Studio".to_string(),
        app_version,
        backend_version,
        build_date,
        build_mode,
        target_arch,
        target_os,
    };

    log::info!("Version info collected successfully: {:?}", version_info);
    Ok(version_info)
}

/// Read version from package.json
fn get_package_version() -> Result<String> {
    // First try to get version from compile-time environment variable
    // This can be set during build process for production builds
    if let Some(version) = option_env!("APP_VERSION") {
        log::info!("Using APP_VERSION environment variable: {}", version);
        return Ok(version.to_string());
    }

    // Try multiple strategies to find the correct package.json
    let search_paths = vec![
        // Strategy 1: Look for package.json in the parent directory of src-tauri
        // This handles the case where we're running from src-tauri directory
        std::env::current_dir()
            .ok()
            .and_then(|dir| dir.parent().map(|p| p.to_path_buf()))
            .map(|dir| dir.join("package.json")),

        // Strategy 2: Look in current directory and walk up
        std::env::current_dir()
            .ok()
            .map(|dir| dir.join("package.json")),

        // Strategy 3: Try relative path from src-tauri
        std::env::current_dir()
            .ok()
            .map(|dir| dir.join("../package.json")),
    ];

    for path_option in search_paths {
        if let Some(package_json_path) = path_option {
            if package_json_path.exists() {
                log::info!("Found package.json at: {:?}", package_json_path);

                match fs::read_to_string(&package_json_path) {
                    Ok(content) => {
                        match serde_json::from_str::<serde_json::Value>(&content) {
                            Ok(package_json) => {
                                if let Some(version) = package_json.get("version").and_then(|v| v.as_str()) {
                                    log::info!("Successfully read version from package.json: {}", version);
                                    return Ok(version.to_string());
                                } else {
                                    log::warn!("package.json found but no version field at: {:?}", package_json_path);
                                }
                            }
                            Err(e) => {
                                log::warn!("Failed to parse package.json at {:?}: {}", package_json_path, e);
                            }
                        }
                    }
                    Err(e) => {
                        log::warn!("Failed to read package.json at {:?}: {}", package_json_path, e);
                    }
                }
            }
        }
    }

    // Fallback: walk up directory tree from current directory
    if let Ok(mut current_dir) = std::env::current_dir() {
        loop {
            let package_json_path = current_dir.join("package.json");
            if package_json_path.exists() {
                log::info!("Found package.json via directory walk at: {:?}", package_json_path);

                if let Ok(content) = fs::read_to_string(&package_json_path) {
                    if let Ok(package_json) = serde_json::from_str::<serde_json::Value>(&content) {
                        if let Some(version) = package_json.get("version").and_then(|v| v.as_str()) {
                            log::info!("Successfully read version from package.json via walk: {}", version);
                            return Ok(version.to_string());
                        }
                    }
                }
            }

            // Move to parent directory
            if let Some(parent) = current_dir.parent() {
                current_dir = parent.to_path_buf();
            } else {
                break;
            }
        }
    }

    // If package.json is not found, fall back to Cargo version
    // This ensures we always have a version number
    log::warn!("Could not find package.json, falling back to Cargo version: {}", env!("CARGO_PKG_VERSION"));
    Ok(env!("CARGO_PKG_VERSION").to_string())
}

/// Get build date (compile time)
fn get_build_date() -> String {
    // Use build date set at compile time via build.rs
    // If BUILD_DATE is not available, fall back to package version info
    match option_env!("BUILD_DATE") {
        Some(date) => date.to_string(),
        None => {
            // Fallback: use a default build date format
            format!("Built with Cargo v{}", env!("CARGO_PKG_VERSION"))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_app_version_info() {
        let result = get_app_version_info().await;
        assert!(result.is_ok());

        let version_info = result.unwrap();

        // Check that all fields are populated
        assert!(!version_info.app_name.is_empty());
        assert!(!version_info.app_version.is_empty());
        assert!(!version_info.backend_version.is_empty());
        assert!(!version_info.build_date.is_empty());
        assert!(!version_info.build_mode.is_empty());
        assert!(!version_info.target_arch.is_empty());
        assert!(!version_info.target_os.is_empty());

        // Check specific values
        assert_eq!(version_info.app_name, "RealityTap Studio");
        assert_eq!(version_info.backend_version, "1.0.0");
        assert!(version_info.build_mode == "debug" || version_info.build_mode == "release");

        println!("Version info: {:?}", version_info);
    }

    #[test]
    fn test_get_package_version() {
        let result = get_package_version();
        // Should either succeed or fall back to Cargo version
        assert!(result.is_ok());

        let version = result.unwrap();
        assert!(!version.is_empty());
        println!("Package version: {}", version);
    }

    #[test]
    fn test_get_build_date() {
        let build_date = get_build_date();
        assert!(!build_date.is_empty());
        println!("Build date: {}", build_date);
    }
}
