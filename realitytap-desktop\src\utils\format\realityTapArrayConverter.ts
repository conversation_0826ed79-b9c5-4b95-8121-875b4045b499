/**
 * RealityTap 通用数组转换器
 * 自动检测版本并调用相应的转换逻辑
 */

import type { ConvertToArrayInput, ConvertToArrayOptions, ConvertToArrayResult } from "@/types/reality-tap-converter";
import { detectRealityTapVersion } from "@/utils/validation/versionDetector";
import { validateRealityTapData } from "@/utils/validation/dataValidator";
import { convertV1ToArray } from "./v1Converter";
import { convertV2ToArray } from "./v2Converter";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 将 RealityTap JSON 数据转换为数组格式
 *
 * 这个函数是主要的入口点，它会：
 * 1. 自动检测输入数据的版本（V1 或 V2）
 * 2. 验证输入数据的完整性和正确性
 * 3. 调用相应版本的转换器进行转换
 * 4. 返回统一格式的转换结果
 *
 * @param jsonData 输入的 JSON 数据，支持多种格式
 * @param options 转换选项配置
 * @returns 转换结果，包含成功状态、数据、错误信息等
 *
 * @example
 * ```typescript
 * // 转换 V1 格式数据
 * const v1Result = convertRealityTapToArray(v1JsonData);
 * if (v1Result.success) {
 *   console.log('V1 数组:', v1Result.data);
 * }
 *
 * // 转换 V2 格式数据，指定进程ID和序列号
 * const v2Result = convertRealityTapToArray(v2JsonData, {
 *   processId: 123,
 *   sequence: 456,
 *   enableLogging: true
 * });
 *
 * // 强制指定版本
 * const forcedResult = convertRealityTapToArray(unknownData, {
 *   forceVersion: 2
 * });
 * ```
 */
export function convertRealityTapToArray(jsonData: ConvertToArrayInput, options: ConvertToArrayOptions = {}): ConvertToArrayResult {
  const startTime = performance.now();

  try {
    // 设置默认选项
    const finalOptions: ConvertToArrayOptions = {
      processId: 0,
      sequence: 0,
      validateInput: true,
      strictMode: false,
      enableLogging: false,
      ...options,
    };

    if (finalOptions.enableLogging) {
      logger.debug(LogModule.WAVEFORM, "[RealityTapConverter] 开始转换", {
        hasForceVersion: !!finalOptions.forceVersion,
        validateInput: finalOptions.validateInput,
        strictMode: finalOptions.strictMode,
        processId: finalOptions.processId,
        sequence: finalOptions.sequence,
        inputDataType: typeof jsonData,
        isArray: Array.isArray(jsonData)
      });
    }

    // 1. 版本检测
    if (finalOptions.enableLogging) {
      logger.debug(LogModule.WAVEFORM, "[RealityTapConverter] 开始版本检测", {
        forceVersion: finalOptions.forceVersion || null
      });
    }

    const versionResult = detectRealityTapVersion(jsonData, finalOptions);
    if (!versionResult.success || !versionResult.version) {
      const errorMsg = `版本检测失败: ${versionResult.details || "未知错误"}`;

      if (finalOptions.enableLogging) {
        logger.error(LogModule.WAVEFORM, "[RealityTapConverter] 版本检测失败", {
          error: versionResult.details,
          inputType: typeof jsonData,
          hasMetadata: !!(jsonData as any)?.Metadata,
          hasPattern: !!(jsonData as any)?.Pattern,
          hasPatternList: !!(jsonData as any)?.PatternList
        });
      }

      return {
        success: false,
        error: errorMsg,
        metadata: {
          totalEvents: 0,
          arrayLength: 0,
          processingTime: performance.now() - startTime,
          detectedVersion: 1, // 默认值
        },
      };
    }

    const detectedVersion = versionResult.version;

    if (finalOptions.enableLogging) {
      logger.info(LogModule.WAVEFORM, "[RealityTapConverter] 版本检测完成", {
        detectedVersion,
        detectionMethod: versionResult.detectionMethod,
        confidence: versionResult.confidence,
        isForced: !!finalOptions.forceVersion
      });
    }

    // 2. 输入数据验证（如果启用）
    if (finalOptions.validateInput) {
      if (finalOptions.enableLogging) {
        logger.debug(LogModule.WAVEFORM, "[RealityTapConverter] 开始数据验证", {
          version: detectedVersion,
          strictMode: finalOptions.strictMode
        });
      }

      const validationResult = validateRealityTapData(jsonData, detectedVersion, finalOptions);

      if (!validationResult.isValid) {
        const errorMsg = `数据验证失败: ${validationResult.errors.join("; ")}`;

        if (finalOptions.enableLogging) {
          logger.error(LogModule.WAVEFORM, "[RealityTapConverter] 数据验证失败", {
            errors: validationResult.errors,
            warnings: validationResult.warnings,
            version: detectedVersion
          });
        }

        return {
          success: false,
          error: errorMsg,
          version: detectedVersion,
          warnings: validationResult.warnings.length > 0 ? validationResult.warnings : undefined,
          metadata: {
            totalEvents: 0,
            arrayLength: 0,
            processingTime: performance.now() - startTime,
            detectedVersion,
          },
        };
      }

      if (finalOptions.enableLogging) {
        if (validationResult.warnings.length > 0) {
          logger.warn(LogModule.WAVEFORM, "[RealityTapConverter] 数据验证完成，存在警告", {
            warnings: validationResult.warnings,
            warningCount: validationResult.warnings.length
          });
        } else {
          logger.debug(LogModule.WAVEFORM, "[RealityTapConverter] 数据验证通过", {
            version: detectedVersion
          });
        }
      }
    } else if (finalOptions.enableLogging) {
      logger.debug(LogModule.WAVEFORM, "[RealityTapConverter] 跳过数据验证", {
        reason: "validateInput = false"
      });
    }

    // 3. 根据版本调用相应的转换器
    let conversionResult: ConvertToArrayResult;

    if (finalOptions.enableLogging) {
      logger.debug(LogModule.WAVEFORM, "[RealityTapConverter] 开始格式转换", {
        targetVersion: detectedVersion,
        converterType: detectedVersion === 1 ? 'V1Converter' : detectedVersion === 2 ? 'V2Converter' : 'Unknown'
      });
    }

    const conversionStartTime = performance.now();

    if (detectedVersion === 1) {
      conversionResult = convertV1ToArray(jsonData as any, finalOptions);
    } else if (detectedVersion === 2) {
      conversionResult = convertV2ToArray(jsonData as any, finalOptions);
    } else {
      const errorMsg = `不支持的版本: ${detectedVersion}`;

      if (finalOptions.enableLogging) {
        logger.error(LogModule.WAVEFORM, "[RealityTapConverter] 不支持的版本", {
          detectedVersion,
          supportedVersions: [1, 2]
        });
      }

      return {
        success: false,
        error: errorMsg,
        version: detectedVersion,
        metadata: {
          totalEvents: 0,
          arrayLength: 0,
          processingTime: performance.now() - startTime,
          detectedVersion,
        },
      };
    }

    const conversionTime = performance.now() - conversionStartTime;

    if (finalOptions.enableLogging) {
      if (conversionResult.success) {
        logger.info(LogModule.WAVEFORM, "[RealityTapConverter] 格式转换成功", {
          version: detectedVersion,
          arrayLength: conversionResult.data?.length || 0,
          totalEvents: conversionResult.metadata?.totalEvents || 0,
          conversionTime: `${conversionTime.toFixed(2)}ms`,
          hasWarnings: !!(conversionResult.warnings && conversionResult.warnings.length > 0)
        });
      } else {
        logger.error(LogModule.WAVEFORM, "[RealityTapConverter] 格式转换失败", {
          version: detectedVersion,
          error: conversionResult.error,
          conversionTime: `${conversionTime.toFixed(2)}ms`
        });
      }
    }

    // 4. 增强转换结果的元数据
    if (conversionResult.success && conversionResult.metadata) {
      conversionResult.metadata.usedForceVersion = !!finalOptions.forceVersion;

      // 如果版本检测置信度较低，添加警告
      if (versionResult.confidence < 0.8) {
        const lowConfidenceWarning = `版本检测置信度较低 (${(versionResult.confidence * 100).toFixed(1)}%)，建议验证转换结果`;
        conversionResult.warnings = conversionResult.warnings || [];
        conversionResult.warnings.push(lowConfidenceWarning);

        if (finalOptions.enableLogging) {
          logger.warn(LogModule.WAVEFORM, "[RealityTapConverter] 版本检测置信度较低", {
            confidence: versionResult.confidence,
            confidencePercentage: `${(versionResult.confidence * 100).toFixed(1)}%`,
            detectionMethod: versionResult.detectionMethod,
            recommendation: "建议验证转换结果"
          });
        }
      }
    }

    const totalProcessingTime = performance.now() - startTime;

    if (finalOptions.enableLogging) {
      const logLevel = conversionResult.success ? 'info' : 'error';
      const logMessage = conversionResult.success ?
        "[RealityTapConverter] 转换流程完成" :
        "[RealityTapConverter] 转换流程失败";

      logger[logLevel](LogModule.WAVEFORM, logMessage, {
        success: conversionResult.success,
        version: detectedVersion,
        arrayLength: conversionResult.data?.length || 0,
        totalEvents: conversionResult.metadata?.totalEvents || 0,
        totalProcessingTime: `${totalProcessingTime.toFixed(2)}ms`,
        hasWarnings: !!(conversionResult.warnings && conversionResult.warnings.length > 0),
        warningCount: conversionResult.warnings?.length || 0,
        processId: finalOptions.processId,
        sequence: finalOptions.sequence,
        error: conversionResult.success ? undefined : conversionResult.error
      });
    }

    return conversionResult;
  } catch (error) {
    const errorMessage = `转换过程中发生未预期的错误: ${error instanceof Error ? error.message : String(error)}`;
    const processingTime = performance.now() - startTime;

    if (options.enableLogging) {
      logger.error(LogModule.WAVEFORM, "[RealityTapConverter] 转换过程发生异常", {
        error: error instanceof Error ? error.message : String(error),
        errorType: error instanceof Error ? error.constructor.name : typeof error,
        stack: error instanceof Error ? error.stack : undefined,
        processingTime: `${processingTime.toFixed(2)}ms`,
        inputType: typeof jsonData,
        isArray: Array.isArray(jsonData),
        processId: options.processId,
        sequence: options.sequence,
        hasForceVersion: !!options.forceVersion,
        validateInput: options.validateInput,
        strictMode: options.strictMode
      });
    }

    return {
      success: false,
      error: errorMessage,
      metadata: {
        totalEvents: 0,
        arrayLength: 0,
        processingTime,
        detectedVersion: 1, // 默认值
      },
    };
  }
}

/**
 * 获取转换器支持的版本列表
 */
export function getSupportedVersions(): Array<{ version: number; description: string }> {
  const supportedVersions = [
    { version: 1, description: "RealityTap V1 格式 - 简单的 Pattern 结构" },
    { version: 2, description: "RealityTap V2 格式 - 增强的 PatternList 结构，支持多马达" },
  ];

  logger.debug(LogModule.WAVEFORM, "[RealityTapConverter] 获取支持的版本列表", {
    supportedVersions: supportedVersions.map(v => v.version),
    totalVersions: supportedVersions.length
  });

  return supportedVersions;
}
