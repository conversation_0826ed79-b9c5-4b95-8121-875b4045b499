# RealityTap 核心库开发者指导文档

## 概述

RealityTap 是一个高性能的触觉反馈算法库，专为移动设备和嵌入式系统设计。本文档是第三方开发者集成和使用 RealityTap 核心库的**完整指南**。

> **💡 重要提示**：从 v1.0 开始，第三方开发者只需要包含一个头文件：
> ```cpp
> #include "realitytap/realitytap_interfaces.h"
> ```

## 目录

1. [快速开始](#快速开始)
2. [API 参考](#api-参考)
3. [核心接口](#核心接口)
4. [初始化流程](#初始化流程)
5. [触觉数据播放](#触觉数据播放)
6. [自定义输出处理器](#自定义输出处理器)
7. [平台适配](#平台适配)
8. [错误处理](#错误处理)
9. [性能优化](#性能优化)
10. [示例代码](#示例代码)
11. [版本信息](#版本信息)

## 快速开始

### 1. 包含必要的头文件

```cpp
#include "realitytap/realitytap_interfaces.h"
```

**注意**：从 v1.0 开始，第三方开发者只需要包含 `realitytap_interfaces.h` 一个头文件，它包含了所有必要的接口定义、数据结构和 API 函数声明。

### 2. 基本使用流程

```cpp
// 1. 创建输出处理器
IHapticOutputHandler* outputHandler = new YourOutputHandler();

// 2. 配置初始化参数
HapticActuatorParams params = {
    0,                    // 设备ID
    170,                  // 谐振频率 (Hz)
    "config.bin",         // 配置文件路径
    SAMPLING_24KHZ,       // 采样率
    outputHandler         // 输出处理器
};

// 3. 初始化库
int32_t result = awa_realitytap_init(&params, 1);

// 4. 启动处理线程
awa_realitytap_append_init();
awa_realitytap_append_start();

// 5. 播放触觉数据
const int32_t hapticData[] = {/* 触觉数据 */};
awa_realitytap_append_haptics(hapticData, sizeof(hapticData)/sizeof(int32_t), 0, 1, 255, 0);
```

## API 参考

### 头文件包含

第三方开发者只需要包含一个头文件：

```cpp
#include "realitytap/realitytap_interfaces.h"
```

此头文件包含：
- 所有接口定义 (IHapticOutputHandler)
- 数据结构 (HapticActuatorParams, SamplingRateType 等)
- 完整的 API 函数声明
- 必要的宏定义

### 主要 API 函数

#### 初始化和配置

##### awa_realitytap_init
```cpp
int32_t awa_realitytap_init(HapticActuatorParams *params, ssize_t count);
```
**功能**：初始化 RealityTap 核心库
**参数**：
- `params` - 触觉执行器参数数组
- `count` - 参数数组长度
**返回值**：0=成功，负数=错误码

##### awa_realitytap_set_amplitude
```cpp
int32_t awa_realitytap_set_amplitude(uint32_t amplitude);
```
**功能**：设置全局振幅
**参数**：`amplitude` - 振幅值 (0-255)

##### awa_realitytap_stop_vibration
```cpp
int32_t awa_realitytap_stop_vibration();
```
**功能**：立即停止所有振动

#### 播放控制

##### awa_realitytap_append_haptics
```cpp
int32_t awa_realitytap_append_haptics(
    const int32_t *data,
    uint32_t patternLen,
    uint32_t intervalMs,
    uint32_t loopNum,
    int32_t amplitude,
    int32_t frequency
);
```
**功能**：播放触觉数据
**参数**：
- `data` - 触觉数据数组 (HE格式)
- `patternLen` - 数据长度 (元素个数)
- `intervalMs` - 循环间隔时间 (毫秒)
- `loopNum` - 循环次数 (1=播放一次)
- `amplitude` - 振幅 (0-255)
- `frequency` - 频率偏移

##### awa_realitytap_append_stop
```cpp
int32_t awa_realitytap_append_stop();
```
**功能**：停止当前播放

##### awa_realitytap_append_init / awa_realitytap_append_start
```cpp
int32_t awa_realitytap_append_init();
int32_t awa_realitytap_append_start();
```
**功能**：初始化播放队列和启动播放线程

#### 预定义效果

##### awa_realitytap_append_prebak
```cpp
int32_t awa_realitytap_append_prebak(uint32_t effect_id, uint32_t strength);
```
**功能**：播放预烘焙效果
**参数**：
- `effect_id` - 效果ID (1=轻点击, 2=重点击, 3=滑动, 4=长按)
- `strength` - 强度 (0-100)

### 数据结构

#### HapticActuatorParams
```cpp
struct HapticActuatorParams {
    uint32_t id;                        // 设备唯一标识符
    uint32_t f0;                        // 谐振频率 (Hz)
    const char* file;                   // 配置文件路径
    SamplingRateType samplingRate;      // 采样率类型
    IHapticOutputHandler* outputHandler; // 输出处理器指针
};
```

#### SamplingRateType
```cpp
enum SamplingRateType {
    SAMPLING_6KHZ = 4,   // 6KHz采样率，基础效果
    SAMPLING_8KHZ = 5,   // 8KHz采样率，通用选择
    SAMPLING_12KHZ = 6,  // 12KHz采样率，中等精度
    SAMPLING_24KHZ = 8   // 24KHz采样率，高精度效果
};
```

#### WaveformProcessingStatus
```cpp
enum WaveformProcessingStatus {
    PROCESSING_ERROR = 0xFF9,    // 处理错误（超时）
    PROCESSING_STOPPED = 0xFF4,  // 处理已停止
    PROCESSING_ACTIVE = 0xFF2,   // 正在处理中
    PROCESSING_READY = 0xFF0     // 系统就绪，处理完成
};
```

### 触觉数据格式

#### HE (Haptic Event) 格式
```cpp
const int32_t heData[] = {
    // 头部信息 (5个元素)
    format_version,    // 格式版本，通常为2
    he_version,        // HE版本，通常为2
    pid,               // 产品ID
    sequence,          // 序列号
    pattern_count,     // 模式数量

    // 模式信息 (每个模式3个元素)
    pattern_index,     // 模式索引
    absolute_time,     // 绝对时间 (ms)
    event_count,       // 事件数量

    // 事件数据 (变长)
    event_type,        // 事件类型 (4096/4097)
    event_length,      // 事件长度
    // ... 事件参数
};
```

#### 事件类型
- **4097** - 简单振动事件：`4097, 5, vib_id, time, intensity, sharpness, duration`
- **4096** - 复杂波形事件：包含多个控制点的复杂波形

### 错误码
- `0` - 操作成功
- `-1` - 一般错误
- 其他负数 - 特定错误码

## 核心接口

### 主要 API 函数

#### 初始化相关

- `awa_realitytap_init()` - 初始化核心库
- `awa_realitytap_reinit()` - 重新初始化
- `awa_realitytap_append_init()` - 初始化处理队列
- `awa_realitytap_append_start()` - 启动处理线程

#### 播放控制

- `awa_realitytap_append_haptics()` - 播放触觉数据
- `awa_realitytap_append_stop()` - 停止播放
- `awa_realitytap_stop_vibration()` - 立即停止振动
- `awa_realitytap_set_amplitude()` - 设置振幅

#### 预定义效果

- `awa_realitytap_play_effect()` - 播放预定义效果
- `awa_realitytap_append_prebak()` - 添加预烘焙效果

### 核心数据结构

#### HapticActuatorParams

```cpp
struct HapticActuatorParams {
    uint32_t id;                        // 设备唯一标识符
    uint32_t f0;                        // 谐振频率 (Hz)
    const char* file;                   // 配置文件路径
    SamplingRateType samplingRate;      // 采样率类型
    IHapticOutputHandler* outputHandler; // 输出处理器
};
```

#### SamplingRateType

```cpp
enum SamplingRateType {
    SAMPLING_6KHZ = 4,   // 6KHz采样率，基础效果
    SAMPLING_8KHZ = 5,   // 8KHz采样率，通用选择
    SAMPLING_12KHZ = 6,  // 12KHz采样率，中等精度
    SAMPLING_24KHZ = 8   // 24KHz采样率，高精度效果
};
```

## 初始化流程

### 1. 准备配置文件

配置文件包含设备相关的参数。确保：
- 文件路径正确且可读
- 文件格式与库版本兼容

### 2. 设置谐振频率

谐振频率是设备的关键物理参数：
- **移动设备**：通常 150-200Hz
- **游戏设备**：通常 100-150Hz  
- **可穿戴设备**：通常 200-300Hz

### 3. 选择采样率

根据应用需求选择合适的采样率：
- **基础应用**：SAMPLING_6KHZ
- **通用应用**：SAMPLING_8KHZ
- **高质量应用**：SAMPLING_12KHZ 或 SAMPLING_24KHZ

### 4. 初始化示例

```cpp
// Android 平台示例
HapticActuatorParams params = {
    0,                                              // 设备ID
    170,                                            // 谐振频率
    "haptic_config.bin",                           // 配置文件
    SAMPLING_24KHZ,                                // 采样率
    new YourOutputHandler()                        // 输出处理器
};

int32_t result = awa_realitytap_init(&params, 1);
if (result != 0) {
    // 处理初始化错误
    return result;
}

// 启动处理系统
awa_realitytap_append_init();
awa_realitytap_append_start();
```

## 触觉数据播放

### 1. 触觉数据格式

RealityTap 支持多种触觉数据格式：

#### HE (Haptic Event) 格式
```cpp
const int32_t heData[] = {
    2, 2, 14754, 720897, 2,           // 格式版本, HE版本, PID, 序列号, 模式数量
    0, 0, 5,                          // 模式索引, 绝对时间, 事件数量
    4097, 5, 0, 0, 45, 0, 48,        // 类型, 长度, 振动ID, 相对时间, 强度, 锐度, 持续时间
    4097, 5, 0, 120, 100, 7, 48,     // 更多事件...
    // ...
};
```

### 2. 播放函数

#### awa_realitytap_append_haptics()

```cpp
int32_t awa_realitytap_append_haptics(
    const int32_t* data,    // 触觉数据数组
    uint32_t patternLen,    // 数据长度
    uint32_t intervalMs,    // 间隔时间(毫秒)
    uint32_t loopNum,       // 循环次数
    int32_t amplitude,      // 振幅 (0-255)
    int32_t frequency       // 频率偏移
);
```

#### 使用示例

```cpp
// 播放单次触觉效果
awa_realitytap_append_haptics(heData, sizeof(heData)/sizeof(int32_t), 0, 1, 255, 0);

// 播放循环效果，间隔500ms，循环3次，振幅50%
awa_realitytap_append_haptics(heData, sizeof(heData)/sizeof(int32_t), 500, 3, 128, 0);
```

### 3. 控制函数

```cpp
// 停止当前播放
awa_realitytap_append_stop();

// 设置全局振幅
awa_realitytap_set_amplitude(128); // 50% 强度

// 立即停止所有振动
awa_realitytap_stop_vibration();
```

## 自定义输出处理器

### 1. 实现 IHapticOutputHandler 接口

```cpp
class CustomOutputHandler : public IHapticOutputHandler {
private:
    // 您的私有成员变量
    std::vector<char> buffer_;
    bool is_active_;
    
public:
    // 构造函数
    CustomOutputHandler() : is_active_(false) {}
    
    // 触觉输出开始
    void onHapticOutputStart() override {
        is_active_ = true;
        buffer_.clear();
        // 初始化您的输出设备
    }
    
    // 处理单个采样点 (性能关键)
    void processWaveformSample(char sample) override {
        if (is_active_) {
            buffer_.push_back(sample);
            // 将采样点发送到硬件
            sendToHardware(sample);
        }
    }
    
    // 数据块开始
    void onWaveformChunkStart() override {
        // 准备接收新的数据块
    }
    
    // 等待数据块处理完成（核心库等待调用者确认）
    int32_t waitChunkProcessingComplete(int32_t sampleCount) override {
        // 确保所有数据都已处理
        flushBuffer();
        // 返回 PROCESSING_READY 表示处理完成
        // 如果处理超时，应返回 PROCESSING_ERROR
        return PROCESSING_READY;
    }
    
    // 触觉输出完成
    void onHapticOutputComplete() override {
        flushBuffer();
        is_active_ = false;
        // 清理资源
    }
    
    // 触觉输出停止
    void onHapticOutputStop() override {
        flushBuffer();
        is_active_ = false;
        // 立即停止输出
    }
    
    // 设置输出振幅
    void setOutputAmplitude(uint32_t amplitude) override {
        // 调整硬件输出振幅
        setHardwareAmplitude(amplitude);
    }
    
private:
    void sendToHardware(char sample) {
        // 实现您的硬件驱动逻辑
    }
    
    void flushBuffer() {
        // 刷新缓冲区到硬件
    }
    
    void setHardwareAmplitude(uint32_t amplitude) {
        // 设置硬件振幅
    }
};
```

### 2. 性能优化建议

#### processWaveformSample() 优化
- 避免动态内存分配
- 使用缓冲区批量处理
- 最小化锁的使用
- 避免复杂计算

```cpp
void processWaveformSample(char sample) override {
    // 好的做法：简单缓冲
    buffer_[buffer_index_++] = sample;
    if (buffer_index_ >= BUFFER_SIZE) {
        flushToHardware();
        buffer_index_ = 0;
    }
    
    // 避免：动态分配
    // std::vector<char> temp; // 不要这样做
}
```

## 平台适配

### Android 平台

```cpp
// Android JNI 示例
extern "C" JNIEXPORT jint JNICALL
Java_com_yourpackage_HapticManager_initRealityTap(JNIEnv *env, jobject thiz) {
    IHapticOutputHandler* handler = new AndroidOutputHandler();
    
    HapticActuatorParams params = {
        0,
        170,
        "haptic_config.bin",
        SAMPLING_24KHZ,
        handler
    };
    
    return awa_realitytap_init(&params, 1);
}
```

### Windows 平台

```cpp
// Windows 示例
class WindowsOutputHandler : public IHapticOutputHandler {
    // 实现 Windows 特定的硬件访问
};

int initializeForWindows() {
    IHapticOutputHandler* handler = new WindowsOutputHandler();
    
    HapticActuatorParams params = {
        0,
        170,
        "haptic_config.bin",
        SAMPLING_24KHZ,
        handler
    };
    
    return awa_realitytap_init(&params, 1);
}
```

## 错误处理

### 常见错误码

- `0` - 成功
- `-1` - 一般错误
- 其他负数 - 特定错误码

### 错误处理示例

```cpp
int32_t result = awa_realitytap_init(&params, 1);
switch (result) {
    case 0:
        // 初始化成功
        break;
    case -1:
        // 检查参数和配置文件
        printf("初始化失败：参数错误或配置文件无效\n");
        break;
    default:
        printf("初始化失败：错误码 %d\n", result);
        break;
}
```

### 调试建议

1. **检查配置文件**：确保配置文件路径正确且可读
2. **验证参数**：检查谐振频率和采样率设置
3. **内存检查**：确保输出处理器生命周期正确
4. **错误码检查**：根据返回的错误码进行相应处理

## 性能优化

### 1. 延迟优化

- 使用合适的采样率
- 优化 processWaveformSample() 实现
- 减少内存分配
- 使用硬件缓冲

### 2. 内存优化

```cpp
class OptimizedHandler : public IHapticOutputHandler {
private:
    // 预分配缓冲区
    static constexpr size_t BUFFER_SIZE = 1024;
    char buffer_[BUFFER_SIZE];
    size_t buffer_index_ = 0;
    
public:
    void processWaveformSample(char sample) override {
        buffer_[buffer_index_++] = sample;
        if (buffer_index_ >= BUFFER_SIZE) {
            // 批量处理
            processBatch(buffer_, BUFFER_SIZE);
            buffer_index_ = 0;
        }
    }
};
```

### 3. 线程安全

```cpp
class ThreadSafeHandler : public IHapticOutputHandler {
private:
    std::mutex mutex_;
    std::atomic<bool> is_active_{false};
    
public:
    void processWaveformSample(char sample) override {
        if (is_active_.load()) {
            // 无锁快速路径
            fastProcess(sample);
        }
    }
    
    void onHapticOutputStart() override {
        std::lock_guard<std::mutex> lock(mutex_);
        is_active_.store(true);
        // 初始化逻辑
    }
};
```

## 示例代码

### 完整的集成示例

以下是一个完整的集成示例，展示了如何初始化和使用 RealityTap：

```cpp
#include "realitytap/realitytap_interfaces.h"

class MyOutputHandler : public IHapticOutputHandler {
public:
    void onHapticOutputStart() override {
        printf("触觉输出开始\n");
    }

    void onHapticOutputComplete() override {
        printf("触觉输出完成\n");
    }

    void onHapticOutputStop() override {
        printf("触觉输出停止\n");
    }

    void onWaveformChunkStart() override {
        // 准备接收数据块
    }

    void processWaveformSample(char sample) override {
        // 处理采样点数据
        sendToHardware(sample);
    }

    int32_t waitChunkProcessingComplete(int32_t sampleCount) override {
        // 等待处理完成
        return PROCESSING_READY;
    }

    void setOutputAmplitude(uint32_t amplitude) override {
        // 设置输出振幅
    }

private:
    void sendToHardware(char sample) {
        // 实现您的硬件输出逻辑
    }
};

int main() {
    // 创建输出处理器
    IHapticOutputHandler* handler = new MyOutputHandler();

    // 配置参数
    HapticActuatorParams params = {
        0,                    // 设备ID
        170,                  // 谐振频率 (Hz)
        "haptic_config.bin",  // 配置文件路径
        SAMPLING_24KHZ,       // 采样率
        handler               // 输出处理器
    };

    // 初始化库
    int32_t result = awa_realitytap_init(&params, 1);
    if (result != 0) {
        printf("初始化失败，错误码: %d\n", result);
        return -1;
    }

    // 启动播放系统
    awa_realitytap_append_init();
    awa_realitytap_append_start();

    // 播放触觉数据
    const int32_t hapticData[] = {
        2, 2, 14754, 720897, 1,           // 头部信息
        0, 0, 1,                          // 模式信息
        4097, 5, 0, 0, 100, 50, 48        // 触觉事件
    };

    awa_realitytap_append_haptics(
        hapticData,
        sizeof(hapticData)/sizeof(int32_t),
        0,    // 间隔
        1,    // 循环次数
        255,  // 振幅
        0     // 频率偏移
    );

    // 清理资源
    delete handler;
    return 0;
}
```

## 高级功能

### 1. 多设备支持

RealityTap 支持同时管理多个触觉设备：

```cpp
// 多设备初始化示例
IHapticOutputHandler* handler1 = new DeviceHandler("/dev/haptic0");
IHapticOutputHandler* handler2 = new DeviceHandler("/dev/haptic1");

HapticActuatorParams params[] = {
    {0, 170, "config1.bin", SAMPLING_24KHZ, handler1},
    {1, 150, "config2.bin", SAMPLING_12KHZ, handler2}
};

int32_t result = awa_realitytap_init(params, 2);
```

### 2. 实时传输协议 (RTP)

支持流式触觉数据传输：

```cpp
// RTP 播放示例
int fd = open("haptic_stream.rtp", O_RDONLY);
awa_realitytap_append_rtp(fd);
```

### 3. 预烘焙效果

使用预定义的触觉效果：

```cpp
// 播放预烘焙效果
awa_realitytap_append_prebak(
    1,    // 效果ID (点击效果)
    80    // 强度 (0-100)
);

// 常见预烘焙效果ID
// 1 - 轻点击
// 2 - 重点击
// 3 - 滑动
// 4 - 长按
```

### 4. 动态参数调整

```cpp
// 运行时调整参数
awa_realitytap_append_param(
    100,  // 间隔时间 (ms)
    128,  // 振幅 (0-255)
    0     // 频率偏移
);
```

## 故障排除

### 常见问题及解决方案

#### 1. 初始化失败

**问题**：`awa_realitytap_init()` 返回 -1

**解决方案**：
- 检查配置文件路径和权限
- 验证谐振频率范围 (50-500Hz)
- 确保输出处理器不为空
- 检查设备ID唯一性

```cpp
// 调试初始化问题
// 检查配置文件是否存在和可读
// 验证谐振频率范围 (50-500Hz)
// 确保输出处理器不为空
```

#### 2. 无触觉输出

**问题**：调用播放函数但无振动输出

**解决方案**：
- 确保调用了 `awa_realitytap_append_start()`
- 检查输出处理器实现
- 验证触觉数据格式
- 检查振幅设置

```cpp
// 检查初始化状态
int32_t result = awa_realitytap_init(&params, 1);
if (result != 0) {
    // 处理初始化错误
    printf("RealityTap 初始化失败，错误码: %d\n", result);
}
```

#### 3. 性能问题

**问题**：触觉输出延迟过高

**解决方案**：
- 优化 `processWaveformSample()` 实现
- 减少动态内存分配
- 使用合适的采样率
- 检查线程优先级

#### 4. 内存泄漏

**问题**：长时间运行后内存使用增长

**解决方案**：
- 确保输出处理器正确释放资源
- 在 `onHapticOutputStop()` 中清理缓冲区
- 使用智能指针管理内存

### 调试工具

#### 1. 日志系统

```cpp
// 在输出处理器中添加日志
void processWaveformSample(char sample) override {
    // 可选：添加调试输出
    // printf("处理采样点: %d\n", sample);
    // 处理逻辑
}
```

#### 2. 性能监控

```cpp
class PerformanceMonitor : public IHapticOutputHandler {
private:
    std::chrono::high_resolution_clock::time_point start_time_;
    size_t sample_count_ = 0;

public:
    void onHapticOutputStart() override {
        start_time_ = std::chrono::high_resolution_clock::now();
        sample_count_ = 0;
    }

    void processWaveformSample(char sample) override {
        sample_count_++;
        // 处理采样点
    }

    void onHapticOutputComplete() override {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time_).count();

        printf("处理完成: %zu 采样点, 耗时 %ld ms\n", sample_count_, duration);
    }
};
```

## 最佳实践

### 1. 资源管理

```cpp
class ResourceManagedHandler : public IHapticOutputHandler {
private:
    std::unique_ptr<HardwareDevice> device_;

public:
    ResourceManagedHandler() {
        device_ = std::make_unique<HardwareDevice>();
    }

    ~ResourceManagedHandler() {
        // 自动清理资源
    }

    void onHapticOutputStart() override {
        if (!device_->isOpen()) {
            device_->open();
        }
    }

    void onHapticOutputStop() override {
        if (device_->isOpen()) {
            device_->close();
        }
    }
};
```

### 2. 错误恢复

```cpp
class RobustHandler : public IHapticOutputHandler {
private:
    int error_count_ = 0;
    static constexpr int MAX_ERRORS = 5;

public:
    void processWaveformSample(char sample) override {
        try {
            sendToHardware(sample);
            error_count_ = 0; // 重置错误计数
        } catch (const std::exception& e) {
            error_count_++;
            printf("处理采样点失败: %s\n", e.what());

            if (error_count_ >= MAX_ERRORS) {
                // 触发错误恢复
                recoverFromError();
            }
        }
    }

private:
    void recoverFromError() {
        // 实现错误恢复逻辑
        printf("开始错误恢复...\n");
        error_count_ = 0;
    }
};
```

### 3. 配置管理

```cpp
struct HapticConfig {
    std::string config_path;
    uint32_t resonant_freq;
    SamplingRateType sampling_rate;
    uint32_t buffer_size;
};

class ConfigManager {
public:
    static HapticConfig loadFromFile(const std::string& path) {
        HapticConfig config;
        // 从文件加载配置
        return config;
    }

    static bool validateConfig(const HapticConfig& config) {
        // 验证配置参数
        return config.resonant_freq >= 50 && config.resonant_freq <= 500;
    }
};
```

## API 参考

### 完整函数列表

#### 初始化和控制
- `awa_realitytap_init(params, count)` - 初始化库
- `awa_realitytap_reinit(params, count)` - 重新初始化
- `awa_realitytap_append_init()` - 初始化处理队列
- `awa_realitytap_append_start()` - 启动处理线程
- `awa_realitytap_stop_vibration()` - 停止振动
- `awa_realitytap_set_amplitude(amplitude)` - 设置振幅

#### 播放功能
- `awa_realitytap_append_haptics(data, len, interval, loop, amp, freq)` - 播放触觉数据
- `awa_realitytap_append_stop()` - 停止播放
- `awa_realitytap_append_on(timeout)` - 简单振动
- `awa_realitytap_append_prebak(effect_id, strength)` - 预烘焙效果
- `awa_realitytap_append_rtp(fd)` - RTP 流播放

#### 高级功能
- `awa_realitytap_play_effect(effect_id, timeout)` - 播放效果
- `awa_realitytap_append_param(interval, amp, freq)` - 设置参数
- `awa_realitytap_append_envelope(data, len, fast)` - 包络播放

### 数据结构参考

详细的数据结构定义请参考 `core/include/realitytap/realitytap_interfaces.h`。

---

## 技术支持

### 联系方式
- 技术文档：本文档包含完整的使用指南
- 问题反馈：联系 RealityTap 开发团队

### 相关文件
- `realitytap/realitytap_interfaces.h` - **第三方开发者头文件**（包含接口定义和API声明）

## 版本信息

### 当前版本：v1.0.0

#### 重要特性
- **单一头文件**：第三方开发者只需包含 `realitytap_interfaces.h`
- **完整API访问**：所有公开函数和接口定义都在一个文件中
- **简化集成**：无需处理多个头文件依赖关系
- **跨平台支持**：统一的导入/导出宏定义

#### 头文件使用
```cpp
// 第三方开发者只需要包含一个头文件
#include "realitytap/realitytap_interfaces.h"
```

#### 迁移指南

**对于新的第三方开发者**：
直接使用新的包含方式，无需额外操作。

**对于现有的第三方开发者**：
如果之前尝试包含多个头文件，现在可以简化为只包含一个文件。



### 兼容性
- **C++标准**：C++17 及以上版本
- **平台支持**：Windows、Android（计划支持 macOS、Linux）
- **编译器**：MSVC、GCC、Clang

### 技术支持
- **文档**：本文档包含完整的使用指南
- **问题反馈**：联系 RealityTap 开发团队

---

**版权所有 © 2025 AWA RealityTap Technology**
