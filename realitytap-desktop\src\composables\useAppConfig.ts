// Application configuration management composable
import { ref, computed, readonly } from "vue";
import { invoke } from "@tauri-apps/api/core";
import type {
  AppConfig,
  PlayEffectDialogConfig,
  ConfigUpdateEvent,
  ConfigValidationResult,
  ConfigOperationOptions
} from "@/types/app-config";
import { SamplingRateType } from "@/types/haptic-types";
import { useMessage } from "naive-ui";

// Global reactive state for app configuration
const appConfig = ref<AppConfig | null>(null);
const isLoading = ref(false);
const lastError = ref<string | null>(null);
const configUpdateEvents = ref<ConfigUpdateEvent[]>([]);

/**
 * Application configuration management composable
 * 提供全局应用配置的读取、保存和管理功能
 */
export function useAppConfig() {
  // 延迟获取 message 实例，避免在组件外部调用 inject()
  const getMessage = () => {
    try {
      return useMessage();
    } catch (error) {
      console.warn('useMessage() called outside of Vue component context, using console fallback');
      return {
        success: (msg: string) => console.log('✅', msg),
        error: (msg: string) => console.error('❌', msg),
        warning: (msg: string) => console.warn('⚠️', msg),
        info: (msg: string) => console.info('ℹ️', msg)
      };
    }
  };

  // Computed properties for easy access to specific config sections
  const playEffectDialogConfig = computed(() => appConfig.value?.playEffectDialog);

  /**
   * Load application configuration from backend
   */
  const loadConfig = async (): Promise<AppConfig | null> => {
    try {
      isLoading.value = true;
      lastError.value = null;

      const config = await invoke<AppConfig>("get_app_config");
      appConfig.value = config;
      
      console.log("应用配置加载成功:", config);
      return config;
    } catch (error: any) {
      const errorMessage = `加载应用配置失败: ${error.message || error}`;
      lastError.value = errorMessage;
      console.error(errorMessage, error);
      getMessage().error(errorMessage);
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Save complete application configuration to backend
   */
  const saveConfig = async (config: AppConfig, options: ConfigOperationOptions = {}): Promise<boolean> => {
    try {
      isLoading.value = true;
      lastError.value = null;

      // Create backup if requested
      if (options.createBackup) {
        await createConfigBackup();
      }

      // Validate configuration if requested
      if (options.validate) {
        const validation = validateConfig(config);
        if (!validation.isValid) {
          throw new Error(`配置验证失败: ${validation.errors.join(", ")}`);
        }
      }

      await invoke("save_app_config_command", { config });
      appConfig.value = config;

      // Emit update event if requested
      if (options.emitEvents !== false) {
        emitConfigUpdateEvent("app_config", null, config);
      }

      console.log("应用配置保存成功");
      getMessage().success("配置保存成功");
      return true;
    } catch (error: any) {
      const errorMessage = `保存应用配置失败: ${error.message || error}`;
      lastError.value = errorMessage;
      console.error(errorMessage, error);
      getMessage().error(errorMessage);
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Reset application configuration to defaults
   */
  const resetConfig = async (): Promise<boolean> => {
    try {
      isLoading.value = true;
      lastError.value = null;

      const defaultConfig = await invoke<AppConfig>("reset_app_config");
      appConfig.value = defaultConfig;

      console.log("应用配置已重置为默认值");
      getMessage().success("配置已重置为默认值");
      return true;
    } catch (error: any) {
      const errorMessage = `重置应用配置失败: ${error.message || error}`;
      lastError.value = errorMessage;
      console.error(errorMessage, error);
      getMessage().error(errorMessage);
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Create backup of current configuration
   */
  const createConfigBackup = async (): Promise<string | null> => {
    try {
      const backupPath = await invoke<string>("create_app_config_backup");
      console.log("配置备份已创建:", backupPath);
      getMessage().success("配置备份已创建");
      return backupPath;
    } catch (error: any) {
      const errorMessage = `创建配置备份失败: ${error.message || error}`;
      console.error(errorMessage, error);
      getMessage().warning(errorMessage);
      return null;
    }
  };

  /**
   * Update PlayEffectDialog configuration
   */
  const updatePlayEffectDialogConfig = async (config: PlayEffectDialogConfig): Promise<boolean> => {
    try {
      const oldConfig = appConfig.value?.playEffectDialog;
      
      await invoke("update_play_effect_dialog_config", { config });
      
      if (appConfig.value) {
        appConfig.value.playEffectDialog = config;
      }

      emitConfigUpdateEvent("playEffectDialog", oldConfig, config);
      console.log("PlayEffectDialog 配置更新成功");
      return true;
    } catch (error: any) {
      const errorMessage = `更新 PlayEffectDialog 配置失败: ${error.message || error}`;
      lastError.value = errorMessage;
      console.error(errorMessage, error);
      getMessage().error(errorMessage);
      return false;
    }
  };

  /**
   * Update last selected motor ID
   */
  const updateLastSelectedMotor = async (motorId: string): Promise<boolean> => {
    try {
      const oldValue = appConfig.value?.playEffectDialog.lastSelectedMotorId;

      await invoke("update_last_selected_motor", { motorId });

      if (appConfig.value) {
        appConfig.value.playEffectDialog.lastSelectedMotorId = motorId;
      }

      emitConfigUpdateEvent("lastSelectedMotorId", oldValue, motorId);
      console.log("马达选择更新成功:", motorId);
      return true;
    } catch (error: any) {
      const errorMessage = `更新马达选择失败: ${error.message || error}`;
      lastError.value = errorMessage;
      console.error(errorMessage, error);
      getMessage().error(errorMessage);
      return false;
    }
  };

  /**
   * Update last selected sampling rate
   */
  const updateLastSelectedSamplingRate = async (samplingRate: SamplingRateType): Promise<boolean> => {
    try {
      const oldValue = appConfig.value?.playEffectDialog.lastSelectedSamplingRate;

      await invoke("update_last_selected_sampling_rate", { samplingRate });

      if (appConfig.value) {
        appConfig.value.playEffectDialog.lastSelectedSamplingRate = samplingRate;
      }

      emitConfigUpdateEvent("lastSelectedSamplingRate", oldValue, samplingRate);
      console.log("采样率选择更新成功:", samplingRate);
      return true;
    } catch (error: any) {
      const errorMessage = `更新采样率选择失败: ${error.message || error}`;
      lastError.value = errorMessage;
      console.error(errorMessage, error);
      getMessage().error(errorMessage);
      return false;
    }
  };



  /**
   * Validate configuration object
   */
  const validateConfig = (config: AppConfig): ConfigValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!config.version) {
      errors.push("配置版本号不能为空");
    }

    // PlayEffectDialog validation
    if (!config.playEffectDialog) {
      errors.push("PlayEffectDialog 配置不能为空");
    } else {
      // Sampling rate validation
      const validSamplingRates = [4, 5, 6, 8];
      if (!validSamplingRates.includes(config.playEffectDialog.lastSelectedSamplingRate)) {
        errors.push("采样率值无效");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  };

  /**
   * Emit configuration update event
   */
  const emitConfigUpdateEvent = (path: string, oldValue: any, newValue: any) => {
    const event: ConfigUpdateEvent = {
      path,
      oldValue,
      newValue,
      timestamp: new Date().toISOString()
    };

    configUpdateEvents.value.unshift(event);
    
    // Keep only last 50 events
    if (configUpdateEvents.value.length > 50) {
      configUpdateEvents.value = configUpdateEvents.value.slice(0, 50);
    }

    console.log("配置更新事件:", event);
  };

  /**
   * Get configuration update history
   */
  const getConfigUpdateHistory = () => {
    return [...configUpdateEvents.value];
  };

  /**
   * Clear configuration update history
   */
  const clearConfigUpdateHistory = () => {
    configUpdateEvents.value = [];
  };

  // Auto-load configuration on first use
  if (!appConfig.value && !isLoading.value) {
    loadConfig();
  }

  return {
    // Reactive state
    appConfig: readonly(appConfig),
    isLoading: readonly(isLoading),
    lastError: readonly(lastError),

    // Computed properties
    playEffectDialogConfig,

    // Configuration management methods
    loadConfig,
    saveConfig,
    resetConfig,
    createConfigBackup,

    // Specific config update methods
    updatePlayEffectDialogConfig,
    updateLastSelectedMotor,
    updateLastSelectedSamplingRate,

    // Utility methods
    validateConfig,
    getConfigUpdateHistory,
    clearConfigUpdateHistory
  };
}
