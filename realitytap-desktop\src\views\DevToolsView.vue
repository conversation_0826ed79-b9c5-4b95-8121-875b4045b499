<!--
  开发工具视图
  仅在开发环境中可用，提供各种开发和调试工具
-->
<template>
  <div class="dev-tools-view">
    <div class="dev-tools-header">
      <div class="header-content">
        <h1>🛠️ 开发工具</h1>
        <p>这个页面仅在开发环境中可用，提供各种开发和调试功能。</p>
      </div>
      <div class="header-actions">
        <n-tooltip placement="bottom">
          <template #trigger>
            <n-button type="primary" @click="handleClose" class="close-button" secondary>
              <template #icon>
                <n-icon>
                  <CloseIcon />
                </n-icon>
              </template>
              关闭开发工具
            </n-button>
          </template>
          返回主页面
        </n-tooltip>
      </div>
    </div>

    <div class="dev-tools-content">
      <!-- 导航标签 -->
      <n-tabs type="line" animated>
        <n-tab-pane name="security" tab="🔒 安全限制">
          <SecurityRestrictionsDemo />
        </n-tab-pane>

        <n-tab-pane name="environment" tab="🌍 环境信息">
          <EnvironmentInfo />
        </n-tab-pane>

        <n-tab-pane name="performance" tab="⚡ 性能监控">
          <PerformanceMonitor />
        </n-tab-pane>

        <n-tab-pane name="logs" tab="📝 日志查看">
          <ConsoleLogViewer />
        </n-tab-pane>
      </n-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NTabs, NTabPane, NButton, NIcon, NTooltip } from "naive-ui";
import { Close as CloseIcon } from "@vicons/ionicons5";
import { useRouter } from "vue-router";
import SecurityRestrictionsDemo from "@/components/dev/SecurityRestrictionsDemo.vue";
import EnvironmentInfo from "@/components/dev/EnvironmentInfo.vue";
import PerformanceMonitor from "@/components/dev/PerformanceMonitor.vue";
import ConsoleLogViewer from "@/components/dev/ConsoleLogViewer.vue";

// 路由
const router = useRouter();

// 关闭开发工具，返回主页
const handleClose = () => {
  router.push("/");
};
</script>

<style scoped>
.dev-tools-view {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.dev-tools-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.header-content {
  text-align: center;
  flex: 1;
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: var(--text-color-1);
}

.header-content p {
  margin: 0;
  color: var(--text-color-2);
}

.header-actions {
  display: flex;
  align-items: center;
}

.close-button {
  background-color: transparent !important;
  border: 1px solid #ff6b6b !important;
  color: #ff6b6b !important;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #ff6b6b !important;
  color: #ffffff !important;
  border-color: #ff6b6b !important;
  transform: scale(1.05);
}

.close-button:active {
  transform: scale(0.95);
}

.dev-tools-content {
  background: var(--card-color);
  border-radius: 8px;
  padding: 16px;
}
</style>
