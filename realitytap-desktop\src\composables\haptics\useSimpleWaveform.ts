/**
 * 简化的只读波形图组合函数
 * Simplified read-only waveform composable for PlayEffectDialog
 *
 * 专门为只读场景设计，无交互、无缓存、无性能优化
 * Designed specifically for read-only scenarios, no interaction, no caching, no performance optimization
 */

import { ref, computed } from "vue";
import type { RenderableEvent, RenderableTransientEvent, RenderableContinuousEvent } from "@/types/haptic-editor";
import { LogModule, logger } from "@/utils/logger/logger";
import { mapFrequencyToColor } from "@/components/editor/waveform/utils/color";
import { INTENSITY_RANGE } from "@/components/editor/waveform/config/waveform-constants";

// 简化的波形图配置接口
export interface SimpleWaveformConfig {
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  colors: {
    background: string;
    grid: string;
    axis: string;
    axisLabel: string;
    transient: string;
    continuous: string;
    playhead: string;
  };
  fontStyle: string;
}

// 波形图数据接口
export interface WaveformData {
  events: RenderableEvent[];
  totalDuration: number; // 自动从events计算得出
  currentTime: number;
}

// 默认配置
export const DEFAULT_SIMPLE_WAVEFORM_CONFIG: SimpleWaveformConfig = {
  padding: {
    top: 20,
    right: 30,
    bottom: 40,
    left: 50,
  },
  colors: {
    background: "#1a1a1a",
    grid: "#333333",
    axis: "#666666",
    axisLabel: "#cccccc",
    transient: "#3a8ee6",
    continuous: "#52c41a",
    playhead: "#ff4d4f",
  },
  fontStyle: '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
};

/**
 * 简化的只读波形图组合函数
 */
export function useSimpleWaveform(initialConfig?: Partial<SimpleWaveformConfig>) {
  // ===== 状态管理 =====

  const canvas = ref<HTMLCanvasElement | null>(null);
  const ctx = ref<CanvasRenderingContext2D | null>(null);
  const config = ref<SimpleWaveformConfig>({
    ...DEFAULT_SIMPLE_WAVEFORM_CONFIG,
    ...initialConfig,
  });

  const canvasWidth = ref(0);
  const canvasHeight = ref(0);

  const data = ref<WaveformData>({
    events: [],
    totalDuration: 0,
    currentTime: 0,
  });

  // ===== 工具函数 =====

  /**
   * 从事件数组计算总时长
   */
  const calculateTotalDurationFromEvents = (events: RenderableEvent[]): number => {
    if (!events || events.length === 0) {
      return 0;
    }

    let maxEndTime = 0;

    events.forEach(event => {
      let endTime = event.startTime;

      if (event.type === 'transient') {
        // 瞬态事件使用 stopTime 或 startTime + width
        endTime = event.stopTime || (event.startTime + (event.width || 25));
      } else if (event.type === 'continuous') {
        // 连续事件使用 stopTime 或 startTime + duration
        endTime = event.stopTime || (event.startTime + (event.duration || 0));
      }

      maxEndTime = Math.max(maxEndTime, endTime);
    });

    return maxEndTime;
  };

  // ===== 辅助函数 =====

  /**
   * 将标准强度范围(0-100)转换为内部使用的归一化强度(0-1)
   * 这个简化版本内部使用0-1范围来简化计算
   */
  const normalizeIntensity = (intensity: number): number => {
    const clampedIntensity = Math.max(INTENSITY_RANGE.MIN, Math.min(INTENSITY_RANGE.MAX, intensity));
    return clampedIntensity / INTENSITY_RANGE.MAX;
  };

  // ===== 计算属性 =====

  const graphWidth = computed(() => canvasWidth.value - config.value.padding.left - config.value.padding.right);

  const graphHeight = computed(() => canvasHeight.value - config.value.padding.top - config.value.padding.bottom);

  // ===== 坐标转换函数 =====

  /**
   * 将时间转换为X坐标
   */
  const mapTimeToX = (time: number): number => {
    if (data.value.totalDuration <= 0) {
      return config.value.padding.left;
    }

    const ratio = time / data.value.totalDuration;
    return config.value.padding.left + ratio * graphWidth.value;
  };

  /**
   * 将强度转换为Y坐标
   * 注意：此函数接收归一化强度(0-1)，用于内部计算
   * 外部数据使用标准强度范围(0-100)，需要通过normalizeIntensity函数转换
   * Y坐标从底部到顶部
   */
  const mapIntensityToY = (intensity: number): number => {
    const clampedIntensity = Math.max(0, Math.min(1, intensity));
    return canvasHeight.value - config.value.padding.bottom - clampedIntensity * graphHeight.value;
  };

  // ===== Canvas管理函数 =====

  /**
   * 初始化Canvas
   */
  const initCanvas = (canvasElement: HTMLCanvasElement): boolean => {
    try {
      canvas.value = canvasElement;
      ctx.value = canvasElement.getContext("2d");

      if (!ctx.value) {
        logger.error(LogModule.GENERAL, "SimpleWaveform: 无法获取Canvas 2D上下文");
        return false;
      }

      // 设置初始尺寸
      const width = canvasElement.clientWidth || canvasElement.offsetWidth || 800;
      const height = canvasElement.clientHeight || canvasElement.offsetHeight || 400;

      resizeCanvas(width, height);

      logger.debug(LogModule.GENERAL, "SimpleWaveform: Canvas初始化成功", {
        width: canvasWidth.value,
        height: canvasHeight.value,
      });

      return true;
    } catch (error) {
      logger.error(LogModule.GENERAL, "SimpleWaveform: Canvas初始化失败", error);
      return false;
    }
  };

  /**
   * 调整Canvas大小
   */
  const resizeCanvas = (width: number, height: number): void => {
    if (!canvas.value || !ctx.value) return;

    // 设置Canvas尺寸
    canvas.value.width = width;
    canvas.value.height = height;
    canvasWidth.value = width;
    canvasHeight.value = height;

    // 重新绘制
    drawWaveform();

    logger.debug(LogModule.GENERAL, "SimpleWaveform: Canvas尺寸调整", {
      width,
      height,
    });
  };

  // ===== 绘制函数 =====

  /**
   * 清空画布
   */
  const clearCanvas = (): void => {
    if (!ctx.value) return;

    ctx.value.fillStyle = config.value.colors.background;
    ctx.value.fillRect(0, 0, canvasWidth.value, canvasHeight.value);
  };

  /**
   * 绘制网格线
   */
  const drawGrid = (): void => {
    if (!ctx.value) return;

    const c = ctx.value;
    const cfg = config.value;

    c.strokeStyle = cfg.colors.grid;
    c.lineWidth = 0.5;

    // 绘制水平网格线（强度轴）- 对应0, 25, 50, 75, 100的强度值
    const intensityValues = [0, 25, 50, 75, 100];
    intensityValues.forEach((intensityValue) => {
      // 将0-100的强度值转换为0-1的归一化值用于坐标计算
      const normalizedIntensity = intensityValue / INTENSITY_RANGE.MAX;
      const y = mapIntensityToY(normalizedIntensity);

      c.beginPath();
      c.moveTo(cfg.padding.left, y);
      c.lineTo(canvasWidth.value - cfg.padding.right, y);
      c.stroke();
    });

    // 绘制垂直网格线（时间轴）
    if (data.value.totalDuration > 0) {
      const verticalLines = 10;
      for (let i = 0; i <= verticalLines; i++) {
        const time = (i / verticalLines) * data.value.totalDuration;
        const x = mapTimeToX(time);

        c.beginPath();
        c.moveTo(x, cfg.padding.top);
        c.lineTo(x, canvasHeight.value - cfg.padding.bottom);
        c.stroke();
      }
    } else {
      logger.debug(LogModule.GENERAL, "SimpleWaveform: totalDuration为0，跳过垂直网格线绘制");
    }
  };

  /**
   * 绘制坐标轴
   */
  const drawAxes = (): void => {
    if (!ctx.value) return;

    const c = ctx.value;
    const cfg = config.value;

    c.strokeStyle = cfg.colors.axis;
    c.lineWidth = 1;

    // X轴
    c.beginPath();
    c.moveTo(cfg.padding.left, canvasHeight.value - cfg.padding.bottom);
    c.lineTo(canvasWidth.value - cfg.padding.right, canvasHeight.value - cfg.padding.bottom);
    c.stroke();

    // Y轴
    c.beginPath();
    c.moveTo(cfg.padding.left, cfg.padding.top);
    c.lineTo(cfg.padding.left, canvasHeight.value - cfg.padding.bottom);
    c.stroke();
  };

  /**
   * 绘制坐标轴标签
   */
  const drawAxisLabels = (): void => {
    if (!ctx.value) return;

    const c = ctx.value;
    const cfg = config.value;

    c.fillStyle = cfg.colors.axisLabel;
    c.font = cfg.fontStyle;

    // Y轴标签（强度）
    c.textAlign = "right";
    c.textBaseline = "middle";

    // 显示0-100的整数强度值
    const intensityLabels = [0, 25, 50, 75, 100];
    intensityLabels.forEach((intensityValue) => {
      // 将0-100的强度值转换为0-1的归一化值用于坐标计算
      const normalizedIntensity = intensityValue / INTENSITY_RANGE.MAX;
      const y = mapIntensityToY(normalizedIntensity);
      c.fillText(intensityValue.toString(), cfg.padding.left - 8, y);
    });

    // X轴标签（时间）
    if (data.value.totalDuration > 0) {
      c.textAlign = "center";
      c.textBaseline = "top";

      const timeLabels = 5;
      for (let i = 0; i <= timeLabels; i++) {
        const time = (i / timeLabels) * data.value.totalDuration;
        const x = mapTimeToX(time);
        const timeText = `${time.toFixed(0)}ms`;
        c.fillText(timeText, x, canvasHeight.value - cfg.padding.bottom + 8);
      }
    }
  };

  /**
   * 绘制瞬态事件（参考InteractiveWaveformCanvas的完整绘制逻辑）
   */
  const drawTransientEvent = (event: RenderableTransientEvent): void => {
    if (!ctx.value) return;

    const c = ctx.value;

    // 计算关键坐标点
    const peakX = mapTimeToX(event.peakTime);
    const startX = mapTimeToX(event.startTime);
    const endX = mapTimeToX(event.stopTime);

    // 使用标准化函数转换强度范围
    const normalizedIntensity = normalizeIntensity(event.intensity);
    const peakY = mapIntensityToY(normalizedIntensity);

    // 基线Y坐标（强度为0的位置，直接使用X轴位置，无间隙）
    const baseY = mapIntensityToY(0);

    // 绘制填充区域（三角形）
    c.beginPath();
    c.moveTo(startX, baseY);
    c.lineTo(peakX, peakY);
    c.lineTo(endX, baseY);
    c.fillStyle = "rgba(255, 140, 0, 0.5)"; // TRANSIENT_FILL_COLOR
    c.fill();

    // 绘制边框线条
    c.beginPath();
    c.moveTo(startX, baseY);
    c.lineTo(peakX, peakY);
    c.lineTo(endX, baseY);
    c.strokeStyle = "rgba(255, 140, 0, 0.5)"; // TRANSIENT_STROKE_COLOR
    c.lineWidth = 1.5; // DEFAULT_WAVEFORM_LINE_WIDTH
    c.stroke();
  };



  /**
   * 绘制连续事件（带渐变色填充）
   */
  const drawContinuousEvent = (event: RenderableContinuousEvent): void => {
    if (!ctx.value || !event.curves || event.curves.length < 2) return;

    const c = ctx.value;

    // 计算所有点的坐标和颜色
    const points: Array<{ x: number; y: number; color: string }> = [];

    event.curves.forEach((point) => {
      const x = mapTimeToX(event.startTime + point.timeOffset);
      // 使用标准化函数转换强度范围
      const normalizedIntensity = normalizeIntensity(point.drawIntensity);
      const y = mapIntensityToY(normalizedIntensity);
      const color = mapFrequencyToColor(point.curveFrequency, false); // 第二个参数为isSelected，这里固定为false

      points.push({ x, y, color });
    });

    // 基线Y坐标（强度为0的位置，直接使用X轴位置，无间隙）
    const baseY = mapIntensityToY(0);

    // 绘制渐变填充区域
    if (points.length >= 2) {
      const gradient = c.createLinearGradient(points[0].x, 0, points[points.length - 1].x, 0);

      for (let i = 0; i < points.length; i++) {
        const stopPosition = i / (points.length - 1);
        gradient.addColorStop(stopPosition, points[i].color);
      }

      c.beginPath();
      c.moveTo(points[0].x, points[0].y);

      // 绘制曲线顶部
      for (let i = 1; i < points.length; i++) {
        c.lineTo(points[i].x, points[i].y);
      }

      // 绘制到基线并闭合路径
      c.lineTo(points[points.length - 1].x, baseY);
      c.lineTo(points[0].x, baseY);
      c.closePath();

      c.fillStyle = gradient;
      c.fill();
    }

    // 绘制曲线描边
    c.strokeStyle = "rgba(48, 158, 155, 0.5)"; // CONTINUOUS_STROKE_COLOR
    c.lineWidth = 1.5;
    c.lineCap = "round";
    c.lineJoin = "round";

    c.beginPath();
    c.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < points.length; i++) {
      c.lineTo(points[i].x, points[i].y);
    }
    c.stroke();
  };

  /**
   * 绘制所有事件
   */
  const drawEvents = (): void => {
    if (!ctx.value) return;

    if (data.value.events.length === 0) {
      return;
    }

    data.value.events.forEach((event) => {
      if (event.type === "transient") {
        drawTransientEvent(event as RenderableTransientEvent);
      } else if (event.type === "continuous") {
        drawContinuousEvent(event as RenderableContinuousEvent);
      } else {
        logger.warn(LogModule.GENERAL, "SimpleWaveform: 未知事件类型", { type: (event as any).type, event });
      }
    });
  };

  /**
   * 绘制播放头
   */
  const drawPlayhead = (): void => {
    if (!ctx.value || data.value.currentTime <= 0) return;

    const c = ctx.value;
    const x = mapTimeToX(data.value.currentTime);

    c.strokeStyle = config.value.colors.playhead;
    c.lineWidth = 2;
    c.lineCap = "round";

    c.beginPath();
    c.moveTo(x, config.value.padding.top);
    c.lineTo(x, canvasHeight.value - config.value.padding.bottom);
    c.stroke();
  };

  /**
   * 完整绘制波形图
   */
  const drawWaveform = (): void => {
    if (!ctx.value) {
      logger.debug(LogModule.GENERAL, "SimpleWaveform: ctx为空，无法绘制");
      return;
    }

    try {
      // 清空画布
      clearCanvas();

      // 绘制网格
      drawGrid();

      // 绘制坐标轴
      drawAxes();

      // 绘制坐标轴标签
      drawAxisLabels();

      // 绘制事件
      drawEvents();

      // 绘制播放头
      drawPlayhead();
    } catch (error) {
      logger.error(LogModule.GENERAL, "SimpleWaveform: 绘制失败", error);
    }
  };

  // ===== 数据更新函数 =====

  /**
   * 更新波形数据
   */
  const updateData = (newData: Partial<WaveformData>): void => {
    // 如果传入了新的events，自动计算totalDuration
    if (newData.events) {
      const calculatedDuration = calculateTotalDurationFromEvents(newData.events);
      data.value = {
        ...data.value,
        ...newData,
        totalDuration: calculatedDuration
      };
    } else {
      data.value = { ...data.value, ...newData };
    }

    // 详细的数据日志，帮助调试
    logger.debug(LogModule.GENERAL, "SimpleWaveform: 数据更新", {
      eventsCount: data.value.events.length,
      totalDuration: data.value.totalDuration,
      currentTime: data.value.currentTime,
      events: data.value.events.map((event) => ({
        type: event.type,
        id: event.id,
        startTime: event.startTime,
        ...(event.type === "transient" ? { intensity: event.intensity } : {}),
        ...(event.type === "continuous"
          ? {
              curvesCount: event.curves?.length || 0,
              duration: event.duration,
            }
          : {}),
      })),
    });

    drawWaveform();
  };

  /**
   * 更新配置
   */
  const updateConfig = (newConfig: Partial<SimpleWaveformConfig>): void => {
    config.value = { ...config.value, ...newConfig };
    drawWaveform();
  };

  /**
   * 设置播放时间
   */
  const setCurrentTime = (time: number): void => {
    data.value.currentTime = Math.max(0, Math.min(data.value.totalDuration, time));
    drawWaveform();
  };

  return {
    // 状态
    canvas,
    ctx,
    config,
    data,
    canvasWidth,
    canvasHeight,
    graphWidth,
    graphHeight,

    // 方法
    initCanvas,
    resizeCanvas,
    drawWaveform,
    updateData,
    updateConfig,
    setCurrentTime,

    // 坐标转换（供外部使用）
    mapTimeToX,
    mapIntensityToY,
  };
}
