/**
 * librtcore 全局单例管理器
 * 基于 Vue 3 Composition API 的纯 Composable 单例模式
 * 管理 librtcore 的全局生命周期、配置变化和组件通信
 */

import { ref, computed, reactive, type Ref, type ComputedRef } from "vue";
import { LogModule, logger } from "@/utils/logger/logger";
import { hapticApi } from "@/utils/api/haptic-api";
import type { HapticActuatorConfig, SamplingRateType } from "@/types/haptic-types";
import type { MotorModel } from "@/types/play-effect-dialog";
import { useLibrtcoreGlobalConfig, LIBRTCORE_CONFIG } from "./useLibrtcoreGlobalConfig";

/**
 * 全局管理器初始化状态枚举
 */
export enum GlobalLibrtcoreState {
  UNINITIALIZED = "uninitialized",
  BACKGROUND_INITIALIZING = "background_initializing",
  INITIALIZING = "initializing",
  INITIALIZED = "initialized",
  REINITIALIZING = "reinitializing",
  ERROR = "error",
  CLEANUP = "cleanup",
}

/**
 * 全局管理器状态接口
 */
export interface LibrtcoreGlobalState {
  /** 初始化状态 */
  initState: GlobalLibrtcoreState;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 当前配置 */
  currentConfig: HapticActuatorConfig | null;
  /** 当前马达模型 */
  currentMotor: MotorModel | null;
  /** 当前采样率 */
  currentSamplingRate: SamplingRateType | null;
  /** 当前实际频率 */
  currentActualFrequency: number | null;
  /** 是否已启动播放系统 */
  isStarted: boolean;
  /** 最后初始化时间 */
  lastInitTime: number | null;
  /** 是否为后台初始化 */
  isBackgroundInit: boolean;
}

/**
 * 错误级别枚举
 */
export enum ErrorLevel {
  FATAL = "fatal", // 致命错误，完全阻止功能
  WARNING = "warning", // 警告错误，显示提示但允许继续
  SILENT = "silent", // 静默错误，后台记录不影响用户
}

/**
 * 错误信息接口
 */
export interface LibrtcoreError {
  level: ErrorLevel;
  message: string;
  details?: string;
  timestamp: number;
  canRetry: boolean;
}

/**
 * 事件回调类型
 */
export type EventCallback = (data?: any) => void;

/**
 * 事件类型枚举
 */
export enum LibrtcoreEvent {
  INITIALIZED = "initialized",
  REINITIALIZED = "reinitialized",
  ERROR = "error",
  CLEANUP = "cleanup",
  CONFIG_CHANGED = "config_changed",
}

/**
 * 全局管理器返回接口
 */
export interface UseLibrtcoreGlobalManagerReturn {
  // 状态
  state: Ref<LibrtcoreGlobalState>;
  error: Ref<LibrtcoreError | null>;

  // 计算属性
  isInitialized: ComputedRef<boolean>;
  canPlay: ComputedRef<boolean>;
  isLoading: ComputedRef<boolean>;
  hasError: ComputedRef<boolean>;
  hasFatalError: ComputedRef<boolean>;

  // 核心方法
  backgroundInitialize: (motor?: MotorModel, samplingRate?: SamplingRateType) => Promise<boolean>;
  ensureInitialized: (motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number) => Promise<boolean>;
  updateConfiguration: (motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number) => Promise<boolean>;
  cleanup: () => Promise<void>;
  syncWithBackend: () => Promise<boolean>;

  // 事件系统
  on: (event: LibrtcoreEvent, callback: EventCallback) => () => void;
  off: (event: LibrtcoreEvent, callback: EventCallback) => void;

  // 错误处理
  clearError: () => void;
  retry: () => Promise<boolean>;

  // 工具方法
  getCurrentConfig: () => HapticActuatorConfig | null;
  isConfigurationChanged: (motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number) => boolean;
}

// ===== 全局状态 =====

// 全局响应式状态
const globalState = ref<LibrtcoreGlobalState>({
  initState: GlobalLibrtcoreState.UNINITIALIZED,
  isLoading: false,
  error: null,
  currentConfig: null,
  currentMotor: null,
  currentSamplingRate: null,
  currentActualFrequency: null,
  isStarted: false,
  lastInitTime: null,
  isBackgroundInit: false,
});

// 全局错误状态
const globalError = ref<LibrtcoreError | null>(null);

// 事件监听器映射
const eventListeners = reactive<Record<LibrtcoreEvent, Set<EventCallback>>>({
  [LibrtcoreEvent.INITIALIZED]: new Set(),
  [LibrtcoreEvent.REINITIALIZED]: new Set(),
  [LibrtcoreEvent.ERROR]: new Set(),
  [LibrtcoreEvent.CLEANUP]: new Set(),
  [LibrtcoreEvent.CONFIG_CHANGED]: new Set(),
});

// 防抖定时器
let debounceTimer: number | null = null;

// 操作队列，确保并发安全
let operationQueue: Promise<any> = Promise.resolve();

// ===== 工具函数 =====

/**
 * 发射事件
 */
function emitEvent(event: LibrtcoreEvent, data?: any): void {
  const listeners = eventListeners[event];
  listeners.forEach((callback) => {
    try {
      callback(data);
    } catch (error) {
      logger.warn(LogModule.GENERAL, `事件回调执行失败: ${event}`, error);
    }
  });
}

/**
 * 设置错误状态
 */
function setError(level: ErrorLevel, message: string, details?: string, canRetry: boolean = true): void {
  globalError.value = {
    level,
    message,
    details,
    timestamp: Date.now(),
    canRetry,
  };

  globalState.value.error = message;

  if (level === ErrorLevel.FATAL) {
    globalState.value.initState = GlobalLibrtcoreState.ERROR;
  }

  emitEvent(LibrtcoreEvent.ERROR, globalError.value);

  logger.error(LogModule.GENERAL, `librtcore 全局管理器错误 [${level}]: ${message}`, { details });
}

/**
 * 清除错误状态
 */
function clearErrorState(): void {
  globalError.value = null;
  globalState.value.error = null;
}

/**
 * 创建 HapticActuatorConfig
 */
function createHapticConfig(motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number): HapticActuatorConfig {
  return {
    id: 1, // 固定使用 ID 1
    motor_drive_freq: actualFrequency || motor.resonantFreq || motor.ratedF0 || 170,
    config_file: motor.configPath,
    sampling_rate: samplingRate,
    buffer_size: 1024,
    timeout_ms: 5000,
    enable_logging: true,
  };
}

/**
 * 序列化错误对象
 */
function serializeError(error: any): any {
  if (error instanceof Error) {
    return {
      name: error.name,
      message: error.message,
      stack: error.stack,
    };
  }
  return error;
}

/**
 * 提取错误消息
 */
function extractErrorMessage(error: any): string {
  if (typeof error === "string") return error;
  if (error?.message) return error.message;
  if (error?.details) return error.details;
  return "未知错误";
}

// ===== 核心业务逻辑 =====

/**
 * 执行初始化流程
 */
async function performInitialization(motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number, isReinit: boolean = false, isBackground: boolean = false): Promise<boolean> {
  try {
    // 设置状态
    globalState.value.initState = isBackground
      ? GlobalLibrtcoreState.BACKGROUND_INITIALIZING
      : isReinit
        ? GlobalLibrtcoreState.REINITIALIZING
        : GlobalLibrtcoreState.INITIALIZING;
    globalState.value.isLoading = true;
    globalState.value.isBackgroundInit = isBackground;
    clearErrorState();

    const config = createHapticConfig(motor, samplingRate, actualFrequency);

    logger.info(LogModule.GENERAL, `开始${isBackground ? "后台" : ""}${isReinit ? "重新" : ""}初始化 librtcore`, {
      motorModel: motor.name,
      samplingRate,
      configPath: motor.configPath,
      isReinit,
      isBackground,
    });

    // 如果是重新初始化，先清理现有资源
    if (isReinit && globalState.value.isStarted) {
      try {
        logger.debug(LogModule.GENERAL, "开始清理现有资源");
        await hapticApi.cleanup();
        logger.info(LogModule.GENERAL, "清理现有资源成功");
      } catch (cleanupError) {
        logger.warn(LogModule.GENERAL, "清理现有资源时出现警告", {
          error: serializeError(cleanupError),
        });
        // 清理失败不阻止重新初始化
      }
    }

    // 初始化 librtcore
    logger.debug(LogModule.GENERAL, `开始调用 ${isReinit ? "reinitialize" : "initialize"} API`);

    try {
      const initResult = isReinit ? await hapticApi.reinitialize([config]) : await hapticApi.initialize([config]);

      logger.info(LogModule.GENERAL, `librtcore ${isReinit ? "重新" : ""}初始化成功`, {
        result: initResult,
        config: {
          id: config.id,
          sampling_rate: config.sampling_rate,
          config_file: config.config_file,
        },
      });
    } catch (initError) {
      logger.error(LogModule.GENERAL, `librtcore ${isReinit ? "重新" : ""}初始化失败`, {
        error: serializeError(initError),
        config: {
          id: config.id,
          sampling_rate: config.sampling_rate,
          config_file: config.config_file,
        },
      });
      throw initError;
    }

    // 启动播放系统（检查是否已经启动）
    logger.debug(LogModule.GENERAL, "开始启动播放系统");

    try {
      const startResult = await hapticApi.start();
      logger.info(LogModule.GENERAL, "播放系统启动成功", { result: startResult });
    } catch (startError) {
      // 如果是"播放系统已启动"错误，不视为失败
      if (startError instanceof Error && startError.message?.includes("播放系统已启动")) {
        logger.info(LogModule.GENERAL, "播放系统已经启动，跳过启动步骤");
      } else {
        logger.error(LogModule.GENERAL, "播放系统启动失败", {
          error: serializeError(startError),
        });
        throw startError;
      }
    }

    // 更新状态
    globalState.value.initState = GlobalLibrtcoreState.INITIALIZED;
    globalState.value.currentConfig = config;
    globalState.value.currentMotor = motor;
    globalState.value.currentSamplingRate = samplingRate;
    globalState.value.currentActualFrequency = actualFrequency || motor.ratedF0 || motor.resonantFreq || 170;
    globalState.value.isStarted = true;
    globalState.value.isLoading = false;
    globalState.value.lastInitTime = Date.now();
    globalState.value.isBackgroundInit = false;

    // 发射事件
    const eventType = isReinit ? LibrtcoreEvent.REINITIALIZED : LibrtcoreEvent.INITIALIZED;
    emitEvent(eventType, { motor, samplingRate, config });

    logger.info(LogModule.GENERAL, `librtcore 全局管理器${isReinit ? "重新" : ""}初始化完成`, {
      motor: motor.name,
      samplingRate,
      isBackground,
    });

    return true;
  } catch (error) {
    const errorMessage = extractErrorMessage(error);
    const errorLevel = isBackground ? ErrorLevel.SILENT : ErrorLevel.FATAL;

    setError(errorLevel, `${isReinit ? "重新" : ""}初始化失败: ${errorMessage}`, `马达: ${motor.name}, 采样率: ${samplingRate}`, true);

    globalState.value.isLoading = false;
    globalState.value.isBackgroundInit = false;

    return false;
  }
}

/**
 * 后台初始化（无感初始化）
 */
async function backgroundInitializeInternal(motor?: MotorModel, samplingRate?: SamplingRateType): Promise<boolean> {
  // 如果已经初始化，直接返回成功
  if (globalState.value.initState === GlobalLibrtcoreState.INITIALIZED) {
    logger.debug(LogModule.GENERAL, "librtcore 已初始化，跳过后台初始化");
    return true;
  }

  // 如果没有提供参数，尝试使用默认配置
  if (!motor || !samplingRate) {
    const { getDefaultConfig } = useLibrtcoreGlobalConfig();
    const defaultConfig = getDefaultConfig();

    if (!defaultConfig.hasValidConfig) {
      logger.debug(LogModule.GENERAL, "后台初始化缺少有效的默认配置，跳过初始化");
      return false;
    }

    motor = defaultConfig.motor!;
    samplingRate = defaultConfig.samplingRate;

    logger.debug(LogModule.GENERAL, "后台初始化使用默认配置", {
      motor: motor.name,
      samplingRate,
    });
  }

  return await performInitialization(motor, samplingRate, undefined, true, false);
}

/**
 * 与后端同步状态
 */
async function syncWithBackendInternal(): Promise<boolean> {
  try {
    logger.debug(LogModule.GENERAL, "开始与后端同步 librtcore 状态");

    const status = await hapticApi.getStatus();

    if (status.is_initialized && status.is_started) {
      // 后端已经初始化并启动，更新前端状态
      globalState.value.initState = GlobalLibrtcoreState.INITIALIZED;
      globalState.value.isStarted = true;
      globalState.value.isLoading = false;
      clearErrorState();

      logger.info(LogModule.GENERAL, "检测到后端已初始化并启动，已同步前端状态", {
        backendInitialized: status.is_initialized,
        backendStarted: status.is_started,
        deviceCount: status.device_count
      });
      return true;
    } else if (status.is_initialized && !status.is_started) {
      // 后端已初始化但未启动
      globalState.value.initState = GlobalLibrtcoreState.INITIALIZED;
      globalState.value.isStarted = false;
      globalState.value.isLoading = false;
      clearErrorState();

      logger.info(LogModule.GENERAL, "检测到后端已初始化但未启动，已同步前端状态");
      return true;
    }

    logger.debug(LogModule.GENERAL, "后端未初始化，保持前端状态", {
      backendInitialized: status.is_initialized,
      backendStarted: status.is_started
    });
    return false;
  } catch (error) {
    logger.warn(LogModule.GENERAL, "状态同步失败", error);
    return false;
  }
}

/**
 * 确保已初始化
 */
async function ensureInitializedInternal(motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number): Promise<boolean> {
  try {
    // 如果已经初始化且配置相同，直接返回
    if (globalState.value.initState === GlobalLibrtcoreState.INITIALIZED && !isConfigurationChangedInternal(motor, samplingRate, actualFrequency)) {
      return true;
    }

    // 如果配置不同，需要重新初始化
    const isReinit = globalState.value.initState === GlobalLibrtcoreState.INITIALIZED;
    return await performInitialization(motor, samplingRate, actualFrequency, isReinit, false);
  } catch (error) {
    // 如果遇到"播放系统已启动"错误，尝试同步状态
    if (error instanceof Error && error.message?.includes("播放系统已启动")) {
      logger.info(LogModule.GENERAL, "检测到后端已启动，尝试同步状态");
      const synced = await syncWithBackendInternal();
      if (synced) {
        // 同步成功后，检查配置是否需要更新
        if (!isConfigurationChangedInternal(motor, samplingRate)) {
          logger.info(LogModule.GENERAL, "状态同步成功，配置匹配，无需重新初始化");
          // 更新当前配置信息
          globalState.value.currentMotor = motor;
          globalState.value.currentSamplingRate = samplingRate;
          return true;
        } else {
          logger.info(LogModule.GENERAL, "状态同步成功，但配置不匹配，需要重新初始化");
          // 配置不匹配，需要重新初始化
          return await performInitialization(motor, samplingRate, undefined, true, false);
        }
      }
    }
    throw error;
  }
}

/**
 * 更新配置（防抖处理）
 */
async function updateConfigurationInternal(motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number): Promise<boolean> {
  return new Promise((resolve) => {
    // 清除之前的防抖定时器
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // 设置新的防抖定时器
    debounceTimer = setTimeout(async () => {
      try {
        const success = await ensureInitializedInternal(motor, samplingRate, actualFrequency);
        if (success) {
          emitEvent(LibrtcoreEvent.CONFIG_CHANGED, { motor, samplingRate, actualFrequency });
        }
        resolve(success);
      } catch (error) {
        logger.error(LogModule.GENERAL, "配置更新失败", error);
        resolve(false);
      }
    }, LIBRTCORE_CONFIG.CONFIG_CHANGE_DEBOUNCE);
  });
}

/**
 * 清理资源
 */
async function cleanupInternal(): Promise<void> {
  if (globalState.value.initState === GlobalLibrtcoreState.CLEANUP || globalState.value.initState === GlobalLibrtcoreState.UNINITIALIZED) {
    return;
  }

  try {
    const previousState = globalState.value.initState;
    globalState.value.initState = GlobalLibrtcoreState.CLEANUP;
    globalState.value.isLoading = true;

    logger.info(LogModule.GENERAL, "开始清理 librtcore 全局资源", {
      previousState,
      isStarted: globalState.value.isStarted,
      hasError: globalState.value.error !== null,
    });

    // 停止播放（优雅处理错误）
    if (globalState.value.isStarted) {
      try {
        await hapticApi.stop();
        logger.debug(LogModule.GENERAL, "播放系统已停止");
      } catch (error) {
        logger.debug(LogModule.GENERAL, "停止播放时出现预期错误（系统可能已停止）", error);
      }
    }

    // 清理资源（优雅处理错误）
    try {
      // 调用标准清理方法
      await hapticApi.cleanup();
      logger.info(LogModule.GENERAL, "librtcore 全局资源最终清理完成");
    } catch (error) {
      logger.debug(LogModule.GENERAL, "最终清理过程中出现预期错误（资源可能已清理）", error);
    }

    // 重置状态
    globalState.value.initState = GlobalLibrtcoreState.UNINITIALIZED;
    globalState.value.currentConfig = null;
    globalState.value.currentMotor = null;
    globalState.value.currentSamplingRate = null;
    globalState.value.currentActualFrequency = null;
    globalState.value.isStarted = false;
    globalState.value.isLoading = false;
    globalState.value.lastInitTime = null;
    clearErrorState();

    // 发射清理事件
    emitEvent(LibrtcoreEvent.CLEANUP);

    logger.info(LogModule.GENERAL, "librtcore 全局状态已重置");
  } catch (error) {
    logger.warn(LogModule.GENERAL, "清理过程中出现意外错误，但状态已重置", error);

    // 确保状态被重置
    globalState.value.initState = GlobalLibrtcoreState.UNINITIALIZED;
    globalState.value.currentConfig = null;
    globalState.value.currentMotor = null;
    globalState.value.currentSamplingRate = null;
    globalState.value.currentActualFrequency = null;
    globalState.value.isStarted = false;
    globalState.value.isLoading = false;
    globalState.value.lastInitTime = null;
    clearErrorState();
  } finally {
    globalState.value.isLoading = false;
  }
}

/**
 * 检查配置是否变化
 */
function isConfigurationChangedInternal(motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number): boolean {
  const current = globalState.value;
  const currentActualFreq = current.currentActualFrequency || current.currentMotor?.ratedF0 || current.currentMotor?.resonantFreq || 170;
  const newActualFreq = actualFrequency || motor.ratedF0 || motor.resonantFreq || 170;

  return !current.currentMotor ||
         !current.currentSamplingRate ||
         current.currentMotor.id !== motor.id ||
         current.currentSamplingRate !== samplingRate ||
         currentActualFreq !== newActualFreq;
}

/**
 * 重试上次失败的操作
 */
async function retryInternal(): Promise<boolean> {
  const current = globalState.value;

  if (!current.currentMotor || !current.currentSamplingRate) {
    logger.warn(LogModule.GENERAL, "没有可重试的配置信息");
    return false;
  }

  if (!globalError.value?.canRetry) {
    logger.warn(LogModule.GENERAL, "当前错误不支持重试");
    return false;
  }

  logger.info(LogModule.GENERAL, "开始重试 librtcore 初始化");
  return await ensureInitializedInternal(current.currentMotor, current.currentSamplingRate);
}

// ===== 队列化操作 =====

/**
 * 将操作加入队列，确保并发安全
 */
function queueOperation<T>(operation: () => Promise<T>): Promise<T> {
  operationQueue = operationQueue.then(operation, operation);
  return operationQueue;
}

// ===== 主要导出函数 =====

/**
 * librtcore 全局单例管理器
 * 使用纯 Composable 模式实现全局状态管理
 */
export function useLibrtcoreGlobalManager(): UseLibrtcoreGlobalManagerReturn {
  // 计算属性
  const isInitialized = computed(() => globalState.value.initState === GlobalLibrtcoreState.INITIALIZED);

  const canPlay = computed(() => isInitialized.value && globalState.value.isStarted && !globalState.value.isLoading && !globalError.value);

  const isLoading = computed(
    () =>
      globalState.value.isLoading ||
      globalState.value.initState === GlobalLibrtcoreState.INITIALIZING ||
      globalState.value.initState === GlobalLibrtcoreState.REINITIALIZING ||
      globalState.value.initState === GlobalLibrtcoreState.BACKGROUND_INITIALIZING
  );

  const hasError = computed(() => globalError.value !== null);

  const hasFatalError = computed(() => globalError.value?.level === ErrorLevel.FATAL);

  // 事件系统
  const on = (event: LibrtcoreEvent, callback: EventCallback): (() => void) => {
    eventListeners[event].add(callback);

    // 返回取消监听的函数
    return () => {
      eventListeners[event].delete(callback);
    };
  };

  const off = (event: LibrtcoreEvent, callback: EventCallback): void => {
    eventListeners[event].delete(callback);
  };

  // 公共方法（队列化处理）
  const backgroundInitialize = (motor?: MotorModel, samplingRate?: SamplingRateType): Promise<boolean> => {
    return queueOperation(() => backgroundInitializeInternal(motor, samplingRate));
  };

  const ensureInitialized = (motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number): Promise<boolean> => {
    return queueOperation(() => ensureInitializedInternal(motor, samplingRate, actualFrequency));
  };

  const syncWithBackend = (): Promise<boolean> => {
    return queueOperation(() => syncWithBackendInternal());
  };

  const updateConfiguration = (motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number): Promise<boolean> => {
    return queueOperation(() => updateConfigurationInternal(motor, samplingRate, actualFrequency));
  };

  const cleanup = (): Promise<void> => {
    return queueOperation(() => cleanupInternal());
  };

  const retry = (): Promise<boolean> => {
    return queueOperation(() => retryInternal());
  };

  // 工具方法
  const getCurrentConfig = (): HapticActuatorConfig | null => {
    return globalState.value.currentConfig;
  };

  const isConfigurationChanged = (motor: MotorModel, samplingRate: SamplingRateType, actualFrequency?: number): boolean => {
    return isConfigurationChangedInternal(motor, samplingRate, actualFrequency);
  };

  const clearError = (): void => {
    clearErrorState();
  };

  return {
    // 状态
    state: globalState,
    error: globalError,

    // 计算属性
    isInitialized,
    canPlay,
    isLoading,
    hasError,
    hasFatalError,

    // 核心方法
    backgroundInitialize,
    ensureInitialized,
    updateConfiguration,
    cleanup,
    syncWithBackend,

    // 事件系统
    on,
    off,

    // 错误处理
    clearError,
    retry,

    // 工具方法
    getCurrentConfig,
    isConfigurationChanged,
  };
}
