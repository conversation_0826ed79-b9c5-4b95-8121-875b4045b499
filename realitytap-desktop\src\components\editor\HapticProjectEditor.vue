<template>
  <n-dialog-provider>
    <div class="project-editor">
      <!-- 顶部控制区 -->
      <div class="editor-header">
        <ProjectNavigationHeader :project-name="projectTitle" :group-chain="selectedGroupChain" :file-name="selectedFileName" @update:projectName="handleProjectRename" />
      </div>

      <!-- 主要编辑区 -->
      <div class="editor-main">
        <!-- 左侧剪辑面板 -->
        <div class="editor-project-files">
          <HapticsPanel v-if="hasHaptics" @select-project-item="selectProjectItem" />
          <EmptyHapticArea v-else />
        </div>

        <!-- 中央波形编辑区（现在包含事件调节面板） -->
        <div class="editor-waveform">
          <n-spin :show="isLoadingEffect || isLoadingAudioWaveform" :description="t('editor.waveform.loading')" size="large">
            <MultiFileTabManager
              ref="waveformManagerRef"
              :active-file-uuid="selectedHapticFileUuid"
              :active-effect="loadedHapticEffect"
              :audio-duration="currentAudioDuration"
              :show-tab-bar="false"
              :max-tabs="999"
              :enable-l-r-u="false"
              @increase-duration="handleIncreaseDuration"
              @tab-switch="handleTabSwitch"
              @tab-close="() => {}"
            />
          </n-spin>
        </div>
      </div>
    </div>
  </n-dialog-provider>
</template>

<script setup lang="ts">
import { useFileWaveformEditorStore } from "@/stores/haptics-editor-store";
import { useProjectStore } from "@/stores/haptics-project-store";
import type { RealityTapEffect } from "@/types/haptic-file";
import { parseRealityTap } from "@/types/haptic-file";
import type { HapticFile, HapticsGroup } from "@/types/haptic-project";
import { getGroupChainByUuid } from "@/utils/groupUtils";
import { invoke } from "@tauri-apps/api/core";
import { NDialogProvider, NSpin, useMessage } from "naive-ui";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import { useI18n } from "@/composables/useI18n";
import { logger, LogModule } from "@/utils/logger/logger";

import EmptyHapticArea from "./haptics/EmptyProjectPrompt.vue";
import HapticsPanel from "./haptics/HapticFileExplorer.vue";
import ProjectNavigationHeader from "./header/ProjectNavigationHeader.vue";
import EnhancedMultiFileWaveformManager from "./waveform/MultiFileTabManager.vue";
import MultiFileTabManager from "./waveform/MultiFileTabManager.vue";

// Stores and UI utilities
const projectStore = useProjectStore();
const message = useMessage();
const { t } = useI18n();

// 组件引用
const waveformManagerRef = ref<InstanceType<typeof EnhancedMultiFileWaveformManager> | null>(null);

// 项目状态
const projectTitle = computed(() => projectStore.activeProject?.projectName || t("editor.project.untitled"));

const selectedHapticFileUuid = ref<string | null>(null);
const selectedGroupId = ref<string | null>(null);

// 新增 Ref 用于存储加载的触觉效果数据
const loadedHapticEffect = ref<RealityTapEffect | null>(null);
// 新增 Ref 用于跟踪加载状态
const isLoadingEffect = ref<boolean>(false);

// 新增 Ref 用于存储音频振幅数据
interface AudioAmplitudeData {
  samples: number[];
  sample_rate: number;
  duration_ms: number;
  max_amplitude: number;
  min_amplitude: number;
}

const audioAmplitudeData = ref<AudioAmplitudeData | null>(null);
const isLoadingAudioWaveform = ref(false);
const currentAudioDuration = ref<number | null>(null); // 存储当前选中文件的音频时长

const selectedHapticFile = computed<HapticFile | null>(() => {
  if (!selectedHapticFileUuid.value || !projectStore.activeProject?.files) return null;
  return projectStore.activeProject.files.find((f) => f.fileUuid === selectedHapticFileUuid.value) || null;
});

// ADD selectedFileName computed property
const selectedFileName = computed<string | null>(() => {
  return selectedHapticFile.value ? selectedHapticFile.value.name : null;
});

// 计算属性
const hasHaptics = computed(() => (projectStore.activeProject?.files?.length ?? 0) > 0);

const selectProjectItem = (selectedItem: HapticFile | HapticsGroup | null) => {
  const isFile = selectedItem && "fileUuid" in selectedItem;
  const isGroup = selectedItem && "groupUuid" in selectedItem;
  if (isFile) {
    selectedHapticFileUuid.value = selectedItem.fileUuid;
    projectStore.selectedFileUuid = selectedItem.fileUuid;
    selectedGroupId.value = selectedItem.group;
  } else if (isGroup) {
    selectedHapticFileUuid.value = null;
    projectStore.selectedFileUuid = null;
    selectedGroupId.value = selectedItem.groupUuid;
  } else {
    // 清空所有选中状态，包括波形编辑器状态
    selectedHapticFileUuid.value = null;
    projectStore.selectedFileUuid = null;
    selectedGroupId.value = null;

    // 清空波形编辑器中的事件数据和相关状态
    loadedHapticEffect.value = null;
    audioAmplitudeData.value = null;
    currentAudioDuration.value = null;
    // 注意：不再需要清空全局store，因为每个文件都有独立的store

    logger.debug(LogModule.GENERAL, "已清空所有选中状态和波形数据");
  }
};

// Handler for increasing duration
const handleIncreaseDuration = (durationInMs: number) => {
  // 获取当前文件的store并增加时长
  if (selectedHapticFileUuid.value) {
    const currentFileStore = useFileWaveformEditorStore(selectedHapticFileUuid.value);
    currentFileStore.increaseTotalDurationByUser(durationInMs);
    message.success(t("editor.duration.increased", { duration: durationInMs }));
  } else {
    message.warning(t("editor.file.selectFirst"));
  }
};

// 音频波形切换处理函数已移除：现在根据音频数据自动显示

// Handler for tab switch from MultiFileWaveformManager
const handleTabSwitch = (fileUuid: string) => {
  logger.debug(LogModule.GENERAL, "ProjectEditor: 接收到标签页切换事件", { fileUuid });

  // 同步项目选中状态，但避免重复加载
  if (projectStore.selectedFileUuid !== fileUuid) {
    selectedHapticFileUuid.value = fileUuid;
    projectStore.selectedFileUuid = fileUuid;

    // 更新选中的分组
    const file = projectStore.activeProject?.files?.find((f) => f.fileUuid === fileUuid);
    if (file) {
      selectedGroupId.value = file.group;
    }

    logger.debug(LogModule.GENERAL, "ProjectEditor: 已同步项目选中状态", { fileUuid });
  }
};

// 监听项目数据变化，同步更新当前选中文件的分组信息
watch(
  () => projectStore.activeProject,
  (newProject) => {
    // 当项目数据更新时，检查当前选中文件的分组是否发生变化
    if (selectedHapticFileUuid.value && newProject?.files) {
      const currentFile = newProject.files.find((f) => f.fileUuid === selectedHapticFileUuid.value);
      if (currentFile && currentFile.group !== selectedGroupId.value) {
        logger.debug(LogModule.GENERAL, "文件分组已更新", {
          fileUuid: selectedHapticFileUuid.value,
          fromGroup: selectedGroupId.value,
          toGroup: currentFile.group,
        });
        selectedGroupId.value = currentFile.group;
      }
    }
  },
  { deep: true }
);

// Watch for changes in selectedHapticFileUuid to load effect data
watch(selectedHapticFileUuid, async (newUuid, oldUuid) => {
  if (newUuid === oldUuid) return;

  // 开始计时
  const loadStartTime = performance.now();
  logger.debug(LogModule.GENERAL, "开始加载文件", {
    newUuid,
    timestamp: loadStartTime.toFixed(2),
  });

  // 立即清理音频数据，防止显示错误的波形
  logger.debug(LogModule.GENERAL, "文件切换：立即清理音频数据，防止显示错误波形");
  audioAmplitudeData.value = null;
  isLoadingAudioWaveform.value = false;
  currentAudioDuration.value = null;

  // 在切换文件前，保存当前文件的状态到缓存
  // 【修复】只有在切换到另一个文件时才保存缓存，关闭文件时不保存缓存
  if (oldUuid && newUuid) {
    try {
      // 获取旧文件的 store 实例
      const oldFileStore = useFileWaveformEditorStore(oldUuid);

      // 【优化】移除事件数量限制，所有打开的文件都创建缓存
      // 如果没有缓存，则创建缓存
      if (!projectStore.hasFileCache(oldUuid)) {
        projectStore.setFileCache(oldUuid, loadedHapticEffect.value, oldFileStore.events);
        logger.debug(LogModule.GENERAL, "文件切换：已创建文件缓存", {
          fileUuid: oldUuid,
          eventCount: oldFileStore.events.length,
        });
      } else {
        // 如果已有缓存，则更新缓存中的事件数据
        projectStore.updateFileCacheEvents(oldUuid, oldFileStore.events);
        logger.debug(LogModule.GENERAL, "文件切换：已更新文件缓存", {
          fileUuid: oldUuid,
          eventCount: oldFileStore.events.length,
        });
      }

      // 如果文件有未保存的修改，标记缓存为已修改状态
      if (projectStore.isFileUnsaved(oldUuid)) {
        const cache = projectStore.getFileCache(oldUuid);
        if (cache) {
          cache.isModified = true;
          logger.debug(LogModule.GENERAL, "文件切换：文件标记为已修改状态", { fileUuid: oldUuid });
        }
      }
    } catch (error) {
      logger.warn(LogModule.GENERAL, "保存文件缓存失败", error);
    }
  } else if (oldUuid && !newUuid) {
    // 【修复】关闭文件时不创建缓存，避免缓存释放问题
    logger.debug(LogModule.GENERAL, "文件关闭：跳过创建缓存", {
      fileUuid: oldUuid,
      reason: "避免缓存释放问题",
    });
  }

  if (!newUuid) {
    loadedHapticEffect.value = null;
    const loadEndTime = performance.now();
    logger.debug(LogModule.GENERAL, `⏱️ 文件加载结束 (清空): 耗时 ${(loadEndTime - loadStartTime).toFixed(2)}ms`);
    return;
  }

  isLoadingEffect.value = true;
  loadedHapticEffect.value = null;
  logger.debug(LogModule.GENERAL, `🔄 设置加载状态: isLoadingEffect = true (时间戳: ${performance.now().toFixed(2)}ms)`);

  const file = projectStore.activeProject?.files?.find((f) => f.fileUuid === newUuid);

  if (!file) {
    message.error(t("editor.file.notFound", { uuid: newUuid }));
    isLoadingEffect.value = false;
    const loadEndTime = performance.now();
    logger.debug(LogModule.GENERAL, `❌ 文件加载失败 (找不到文件): 耗时 ${(loadEndTime - loadStartTime).toFixed(2)}ms`);
    return;
  }

  // 【优化】检查是否有缓存数据，优先使用缓存（移除isModified限制）
  const cacheCheckTime = performance.now();
  const cachedData = projectStore.getFileCache(newUuid);
  logger.debug(LogModule.GENERAL, `🔍 缓存检查耗时: ${(performance.now() - cacheCheckTime).toFixed(2)}ms`);

  if (cachedData) {
    const cacheLoadTime = performance.now();
    logger.debug(LogModule.GENERAL, `🔄 文件切换：使用缓存数据加载文件 ${newUuid}，修改状态: ${cachedData.isModified}，事件数量: ${cachedData.currentEvents.length}`);
    loadedHapticEffect.value = cachedData.originalData;
    logger.debug(LogModule.GENERAL, `⚡ 缓存数据加载耗时: ${(performance.now() - cacheLoadTime).toFixed(2)}ms`);
    // 注意：这里不直接设置 waveformEditorStore.events，而是让 WaveformEditor 组件处理
    // 缓存的事件数据将在 WaveformEditor 中通过 flattenRealityTapEffect 处理后设置
  } else {
    logger.debug(LogModule.GENERAL, `💾 文件切换：没有找到缓存数据，将从磁盘加载文件 ${newUuid}`);
  }

  // 音频振幅数据加载逻辑 - 使用 Store 管理，支持音频和视频文件
  const audioLoadStartTime = performance.now();
  const associatedMedia = file.associatedAudio || file.associatedVideo;
  if (associatedMedia && associatedMedia.trim() !== "") {
    isLoadingAudioWaveform.value = true;
    const mediaType = file.associatedVideo ? 'video' : 'audio';
    logger.debug(LogModule.GENERAL, `🎵 开始加载${mediaType}数据: ${associatedMedia} (时间戳: ${audioLoadStartTime.toFixed(2)}ms)`);

    try {
      const projectDir = projectStore.projectPath;
      if (!projectDir) {
        throw new Error("项目路径未设置");
      }

      // 获取当前文件的 waveform store
      const currentFileStore = useFileWaveformEditorStore(newUuid);


      // 1. 先判断是否需要获取音频信息
      if (!file.audioInfo) {
        try {
          const audioInfoStartTime = performance.now();
          logger.debug(LogModule.GENERAL, `🎵 开始获取${mediaType}元数据:`, associatedMedia);

          // 根据文件类型调用相应的后端命令
          const audioInfo: { durationMs: number; sampleRate: number } | null = file.associatedVideo
            ? await invoke("get_video_audio_info", {
                projectDirPath: projectDir,
                videoRelativePath: associatedMedia,
              })
            : await invoke("get_audio_info", {
                projectDirPath: projectDir,
                audioRelativePath: associatedMedia,
              });

          if (audioInfo) {
            const newAudioInfo = {
              durationMs: audioInfo.durationMs,
              sampleRate: audioInfo.sampleRate,
            };
            currentAudioDuration.value = newAudioInfo.durationMs;
            projectStore.updateFileAudioInfo(file.fileUuid, newAudioInfo);
            await projectStore.handleSaveCurrentProject();
            logger.debug(LogModule.GENERAL, `🎵 音频元数据获取成功，耗时: ${(performance.now() - audioInfoStartTime).toFixed(2)}ms`, newAudioInfo);
          }
        } catch (audioInfoErr) {
          console.error("获取音频信息失败:", audioInfoErr);
          // 失败时不阻止波形获取流程继续
        }
      } else {
        // 已有音频信息，直接使用
        logger.debug(LogModule.GENERAL, "🎵 使用已有音频信息:", file.audioInfo);
        currentAudioDuration.value = file.audioInfo.durationMs;
      }

      // 2. 使用 Store 加载音频振幅数据
      try {
        const amplitudeStartTime = performance.now();
        logger.debug(LogModule.GENERAL, `🎵 通过 Store 加载${mediaType}振幅数据:`, { projectDir, mediaRelativePath: associatedMedia });

        // 确保关联媒体文件路径不为空
        if (!associatedMedia || typeof associatedMedia !== 'string') {
          throw new Error("媒体文件路径为空或无效");
        }
        await currentFileStore.loadAudioData(projectDir, associatedMedia, 4096);

        // 从 Store 获取加载的音频数据，用于向后兼容
        const storeAudioData = currentFileStore.getAudioData();

        if (storeAudioData) {
          audioAmplitudeData.value = storeAudioData;
          logger.debug(LogModule.GENERAL, `🎵 音频振幅数据加载成功，耗时: ${(performance.now() - amplitudeStartTime).toFixed(2)}ms`, {
            samplesCount: storeAudioData.samples?.length || 0,
            duration: storeAudioData.duration_ms,
            sampleRate: storeAudioData.sample_rate,
          });
        } else {
          throw new Error("Store 中没有音频数据");
        }
      } catch (amplitudeErr) {
        console.error("获取音频振幅数据失败:", amplitudeErr);
        audioAmplitudeData.value = null;
        const errorMsg = amplitudeErr instanceof Error ? amplitudeErr.message : typeof amplitudeErr === "string" ? amplitudeErr : t("errors.unknown");
        message.error(t("editor.audio.amplitudeLoadFailed", { error: errorMsg }));
      }
    } catch (err: any) {
      audioAmplitudeData.value = null;
      if (!file.audioInfo) {
        currentAudioDuration.value = null;
      }
      console.error("音频处理失败:", err);
      message.error(t("editor.audio.processingFailed", { error: err?.message || err?.toString() || t("errors.unknown") }));
    } finally {
      isLoadingAudioWaveform.value = false;
      logger.debug(LogModule.GENERAL, `🎵 音频数据加载完成，总耗时: ${(performance.now() - audioLoadStartTime).toFixed(2)}ms`);
    }
  } else {
    // 没有关联音频或视频，清理 Store 中的音频数据
    const currentFileStore = useFileWaveformEditorStore(newUuid);
    currentFileStore.clearAudioData();

    audioAmplitudeData.value = null;
    currentAudioDuration.value = file.audioInfo?.durationMs || null;
    logger.debug(LogModule.GENERAL, `🎵 文件无关联媒体，已清理 Store 音频数据，音频处理耗时: ${(performance.now() - audioLoadStartTime).toFixed(2)}ms`);
  }

  // 【优化】只有在没有缓存时才从磁盘加载文件
  if (!cachedData) {
    try {
      const diskLoadStartTime = performance.now();
      logger.debug(LogModule.GENERAL, `💿 开始从磁盘加载文件 ${newUuid} (时间戳: ${diskLoadStartTime.toFixed(2)}ms)`);
      const fullPath = await projectStore.getResolvedFilePath(file);
      if (!fullPath) {
        message.error(t("editor.file.pathResolveFailed", { name: file.name }));
        isLoadingEffect.value = false;
        const loadEndTime = performance.now();
        logger.debug(LogModule.GENERAL, `❌ 文件路径解析失败: 耗时 ${(loadEndTime - loadStartTime).toFixed(2)}ms`);
        return;
      }

      const fileReadStartTime = performance.now();
      const fileContent = await projectStore.readFileContentFromBackend(fullPath);
      logger.debug(LogModule.GENERAL, `📖 文件读取耗时: ${(performance.now() - fileReadStartTime).toFixed(2)}ms`);

      const parseStartTime = performance.now();
      const effectData = parseRealityTap(fileContent);
      logger.debug(LogModule.GENERAL, `🔍 文件解析耗时: ${(performance.now() - parseStartTime).toFixed(2)}ms`);

      loadedHapticEffect.value = effectData;
      logger.debug(LogModule.GENERAL, `✅ 文件切换：从磁盘加载文件 ${newUuid} 完成，总耗时: ${(performance.now() - diskLoadStartTime).toFixed(2)}ms`);
    } catch (error) {
      console.error(`加载触觉文件失败 "${file.name}":`, error);
      let errorMessage = t("errors.unknown");
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      }
      message.error(t("editor.file.loadFailed", { name: file.name, error: errorMessage }));
      loadedHapticEffect.value = null;
      const loadEndTime = performance.now();
      logger.debug(LogModule.GENERAL, `❌ 文件加载失败: 耗时 ${(loadEndTime - loadStartTime).toFixed(2)}ms`);
    } finally {
      setTimeout(() => {
        isLoadingEffect.value = false;
        const loadEndTime = performance.now();
        logger.debug(LogModule.GENERAL, `🔄 设置加载状态: isLoadingEffect = false，总耗时: ${(loadEndTime - loadStartTime).toFixed(2)}ms`);
      }, 300);
    }
  } else {
    // 使用缓存数据时也需要结束加载状态
    setTimeout(() => {
      isLoadingEffect.value = false;
      const loadEndTime = performance.now();
      logger.debug(LogModule.GENERAL, `⚡ 缓存加载完成: isLoadingEffect = false，总耗时: ${(loadEndTime - loadStartTime).toFixed(2)}ms`);
    }, 100);
  }

  // 音频波形显示状态同步已移除：现在根据音频数据自动显示
});

// 保存函数内部的防重复状态
let isInternalSaving = false;

// 保存当前选中的.he文件
const saveCurrentHeFile = async () => {
  // 函数级别的防重复检查
  if (isInternalSaving) {
    logger.debug(LogModule.GENERAL, "saveCurrentHeFile: 函数正在执行中，忽略重复调用");
    return;
  }

  isInternalSaving = true;

  try {
    // 优先使用 selectedHapticFileUuid，如果没有则使用 projectStore.selectedFileUuid
    const fileUuidToSave = selectedHapticFileUuid.value || projectStore.selectedFileUuid;

    if (!fileUuidToSave) {
      console.warn("保存失败：没有选中的文件");
      message.warning(t("editor.file.noFileSelected"));
      return;
    }

    // 获取当前文件对应的 store 实例
    const currentFileStore = useFileWaveformEditorStore(fileUuidToSave);

    logger.debug(LogModule.GENERAL, "保存操作开始", {
      selectedHapticFileUuid: selectedHapticFileUuid.value,
      projectStoreSelectedFileUuid: projectStore.selectedFileUuid,
      fileUuidToSave,
      eventsCount: currentFileStore.events?.length || 0,
    });

    // 检查是否有事件数据可以保存 - 使用当前文件的 store
    if (!currentFileStore.events || currentFileStore.events.length === 0) {
      console.warn("保存失败：没有事件数据", {
        fileUuid: fileUuidToSave,
        currentFileStoreEvents: currentFileStore.events?.length || 0,
      });
      message.warning(t("editor.file.noEventData"));
      return;
    }

    logger.debug(LogModule.GENERAL, `开始保存文件: ${fileUuidToSave}，事件数量: ${currentFileStore.events.length}`);
    const events = currentFileStore.events;
    const totalDuration = currentFileStore.getEffectiveDuration;
    await projectStore.saveHeFile(fileUuidToSave, events, totalDuration);
    logger.debug(LogModule.GENERAL, "文件保存成功");
    message.success(t("editor.file.saveSuccess"));
  } catch (error: any) {
    console.error("保存文件失败:", error);
    message.error(t("editor.file.saveFailed", { error: error.message || t("errors.unknown") }));
  } finally {
    // 延迟重置内部状态
    setTimeout(() => {
      isInternalSaving = false;
      logger.debug(LogModule.GENERAL, "saveCurrentHeFile: 内部状态重置");
    }, 500);
  }
};

// 防抖保存状态
const isSaving = ref(false);
let saveTimeout: number | null = null;

// 键盘快捷键处理
const handleKeyDown = (event: KeyboardEvent) => {
  // Ctrl+S 保存当前文件
  if (event.ctrlKey && event.key === "s") {
    event.preventDefault();
    event.stopPropagation();

    // 防止重复保存 - 更严格的检查
    if (isSaving.value) {
      logger.debug(LogModule.GENERAL, "保存操作正在进行中，忽略重复请求");
      return;
    }

    // 清除之前的超时
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    logger.debug(LogModule.GENERAL, "开始保存操作");
    isSaving.value = true;

    saveCurrentHeFile()
      .then(() => {
        logger.debug(LogModule.GENERAL, "保存操作完成");
      })
      .catch((error) => {
        console.error("保存操作失败:", error);
      })
      .finally(() => {
        // 延迟重置状态，确保不会立即重复触发
        saveTimeout = setTimeout(() => {
          isSaving.value = false;
          logger.debug(LogModule.GENERAL, "保存状态重置");
        }, 1000); // 增加到1秒
      });
  }
};

// 组件挂载时添加键盘监听器
onMounted(() => {
  document.addEventListener("keydown", handleKeyDown);
});

// 组件卸载时移除键盘监听器和清理超时
onUnmounted(() => {
  document.removeEventListener("keydown", handleKeyDown);

  // 清理超时
  if (saveTimeout) {
    clearTimeout(saveTimeout);
    saveTimeout = null;
  }

  // 重置保存状态
  isSaving.value = false;
  isInternalSaving = false;
});

// 处理项目重命名
const handleProjectRename = async (newTitle: string) => {
  if (newTitle.trim() !== projectTitle.value && newTitle.trim() !== "") {
    try {
      const success = await projectStore.renameCurrentProject(newTitle);
      if (success) {
        // 项目重命名成功后，刷新最近项目列表
        message.success(t("editor.project.renameSuccess"));
      } else {
        message.error(t("editor.project.renameFailed"));
      }
    } catch (error) {
      let errorMsg = t("errors.unknown");
      if (error instanceof Error) {
        errorMsg = error.message;
      } else if (typeof error === "string") {
        errorMsg = error;
      }
      message.error(t("editor.project.renameFailedWithError", { error: errorMsg }));
    }
  }
};

// ADD selectedGroupChain computed property
const selectedGroupChain = computed(() => {
  if (selectedGroupId.value && projectStore.activeProject?.groups) {
    return getGroupChainByUuid(projectStore.activeProject.groups, selectedGroupId.value);
  }
  return [];
});
</script>

<style scoped>
/* ===== 主容器背景 ===== */
.project-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1a1a1a;
  color: #e6e6e6;
  padding: 18px;
  box-sizing: border-box;
}

/* ===== 顶部 Header 卡片化 ===== */
.editor-header {
  display: flex;
  align-items: center;
  background: #23272e;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.18);
  padding: 0.5rem 1.5rem;
  margin-bottom: 16px;
  min-height: 56px;
}

/* ProjectHeader应占据主要空间 */
.editor-header :deep(.project-header) {
  flex: 1;
}

/* 确保header中的组件间有适当间距 */
.editor-header > * {
  margin-right: 16px;
}

.editor-header > :last-child {
  margin-right: 0;
}

/* ===== 主内容区布局 ===== */
.editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: 100%;
  gap: 16px;
}

/* ===== 左侧文件区卡片化 ===== */
.editor-project-files {
  width: 250px;
  min-width: 250px;
  background: #20232a;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.14);
  margin-right: 0; /* 由gap控制间距 */
  display: flex;
  flex-direction: column;
  padding: 0px;
}

/* ===== 中部波形编辑区卡片化（现在包含事件调节面板） ===== */
.editor-waveform {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  background: #20232a;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  margin: 0 0;
  padding: 0px;
  min-width: 0;
}

::v-deep(.n-spin) {
  height: 100%;
  background: transparent;
  border-radius: 12px;
}
::v-deep(.n-spin-content) {
  height: 100%;
  background: transparent;
}
::v-deep(.n-spin-container) {
  height: 100%;
  background: transparent;
}
::v-deep(.n-spin-body) {
  position: absolute;
  top: 11%;
}
::v-deep(.n-spin-description) {
  font-size: 14px;
  margin-top: 56px;
}

.editor-header {
  font-weight: 600;
  font-size: 1.1rem;
}
</style>
