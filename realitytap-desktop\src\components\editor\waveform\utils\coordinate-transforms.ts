/**
 * 坐标转换工具函数
 * 专门处理时间、强度与画布坐标之间的转换
 */

import {
  SCROLL_BOUNDARY_TOLERANCE,
  INTENSITY_RANGE,
  INTENSITY_PRECISION,
  LAYOUT_CONSTANTS,
  COORDINATE_PRECISION
} from "../config/waveform-constants";

/**
 * 计算虚拟滚动的偏移量
 */
export function calculateVirtualScrollOffset(
  scrollLeft: number,
  logicalWidth: number,
  availableWidth: number,
  paddingLeft: number,
  paddingRight?: number
): number {
  // 计算可滚动的逻辑范围（去除padding）
  const logicalScrollableWidth = logicalWidth - paddingLeft;
  const rightPadding = paddingRight ?? LAYOUT_CONSTANTS.RIGHT_PADDING;
  const availableScrollableWidth = availableWidth - paddingLeft - rightPadding;

  // 计算最大滚动距离
  const maxScrollLeft = logicalScrollableWidth - availableScrollableWidth;

  if (maxScrollLeft <= 0) {
    return 0; // 无需滚动
  }

  // 使用更严格的边界检查，减少浮点数精度问题
  const tolerance = SCROLL_BOUNDARY_TOLERANCE;

  // 如果scrollLeft接近0，直接返回0，确保能完全滚动到最左侧
  if (scrollLeft <= tolerance) {
    return 0;
  }

  // 如果scrollLeft接近或超过最大值，返回最大偏移量
  if (scrollLeft >= maxScrollLeft - tolerance) {
    return maxScrollLeft;
  }

  // 计算滚动比例，确保在[0,1]范围内
  const scrollRatio = Math.max(0, Math.min(scrollLeft / maxScrollLeft, 1));

  // 虚拟偏移量应该与Canvas的移动保持一致
  // 使用线性插值确保平滑过渡
  const virtualOffset = scrollRatio * maxScrollLeft;

  // 在接近边界时使用更高精度，中间区域保持原有精度
  if (scrollLeft >= maxScrollLeft - tolerance * 2 || scrollLeft <= tolerance * 2) {
    return virtualOffset; // 边界附近不进行舍入
  }

  // 对结果进行微调，确保边界处的稳定性
  return Math.round(virtualOffset * COORDINATE_PRECISION.VIRTUAL_OFFSET_MULTIPLIER) / COORDINATE_PRECISION.VIRTUAL_OFFSET_MULTIPLIER;
}

/**
 * 时间映射到X坐标（支持虚拟滚动）
 */
export function mapTimeToX(
  time: number,
  effectiveDuration: number,
  areaWidth: number,
  paddingLeft: number,
  safeOffset: number,
  virtualOffset: number = 0
): number {
  // 改进边界检查：同时检查有效时长和时间值
  if (effectiveDuration <= 0 || time < 0) return paddingLeft + safeOffset;

  // 计算逻辑X坐标（基于逻辑宽度）
  const logicalX = (time / effectiveDuration) * areaWidth;

  // 应用虚拟偏移量，转换为物理坐标
  const physicalX = logicalX - virtualOffset + paddingLeft + safeOffset;

  // 不限制坐标范围，让调用方决定是否需要裁剪
  // 这样可以确保部分可见的事件能够正确绘制
  return physicalX;
}

/**
 * 时间映射到逻辑X坐标（不考虑虚拟滚动偏移）
 */
export function mapTimeToLogicalX(
  time: number,
  effectiveDuration: number,
  logicalAreaWidth: number,
  paddingLeft: number,
  safeOffset: number
): number {
  // 改进边界检查：同时检查有效时长和时间值
  if (effectiveDuration <= 0 || time < 0) return paddingLeft + safeOffset;
  const x = (time / effectiveDuration) * logicalAreaWidth;
  return paddingLeft + safeOffset + Math.max(0, Math.min(x, logicalAreaWidth));
}

/**
 * 强度映射到Y坐标
 */
export function mapIntensityToY(
  intensity: number,
  areaHeight: number,
  paddingTop: number,
  canvasHeight: number,
  paddingBottom: number
): number {
  // 使用常量定义的强度范围，确保一致性
  const normalizedIntensity = Math.max(INTENSITY_RANGE.MIN, Math.min(INTENSITY_RANGE.MAX, intensity));
  const y = paddingTop + areaHeight - (normalizedIntensity / INTENSITY_RANGE.MAX) * areaHeight;
  return Math.max(paddingTop, Math.min(y, canvasHeight - paddingBottom));
}

/**
 * Y坐标映射到强度
 */
export function mapYToIntensity(y: number, areaHeight: number, paddingTop: number): number {
  const intensity = INTENSITY_RANGE.MAX - ((y - paddingTop) / areaHeight) * INTENSITY_RANGE.MAX;
  return Math.floor(Math.max(INTENSITY_RANGE.MIN, Math.min(INTENSITY_RANGE.MAX, intensity)));
}

/**
 * X坐标差值映射到时间偏移（支持虚拟滚动）
 */
export function mapXToTimeOffset(
  pixelOffset: number,
  areaWidth: number,
  effectiveDuration: number,
  logicalAreaWidth?: number
): number {
  if (effectiveDuration <= 0 || areaWidth <= 0) return 0;
  // 如果提供了逻辑宽度，使用逻辑宽度进行计算以保持精度
  const calculationWidth = logicalAreaWidth || areaWidth;
  return (pixelOffset / calculationWidth) * effectiveDuration;
}

/**
 * X偏移到时间偏移（支持虚拟滚动）
 */
export function mapXOffsetToTimeOffset(
  pixelOffset: number,
  areaWidth: number,
  effectiveDuration: number,
  logicalAreaWidth?: number
): number {
  if (effectiveDuration <= 0 || areaWidth <= 0) return 0;
  // 如果提供了逻辑宽度，使用逻辑宽度进行计算以保持精度
  const calculationWidth = logicalAreaWidth || areaWidth;
  return (pixelOffset / calculationWidth) * effectiveDuration;
}

/**
 * X坐标转换为时间点（支持虚拟滚动）
 */
export function convertXToTime(
  x: number,
  areaWidth: number,
  effectiveDuration: number,
  virtualOffset: number = 0,
  logicalAreaWidth?: number
): number {
  if (effectiveDuration <= 0 || areaWidth <= 0) return 0;

  // 将物理坐标转换为逻辑坐标
  const logicalX = x + virtualOffset;
  const calculationWidth = logicalAreaWidth || areaWidth;
  const relativeX = Math.max(0, Math.min(logicalX, calculationWidth));
  const time = (relativeX / calculationWidth) * effectiveDuration;

  // 根据配置决定是否取整
  return Math.max(0, COORDINATE_PRECISION.TIME_COORDINATE_ROUND ? Math.round(time) : time);
}

/**
 * 判断点是否在半径范围内
 */
export function isPointInRadius(x1: number, y1: number, x2: number, y2: number, radius: number): boolean {
  const dx = x1 - x2;
  const dy = y1 - y2;
  return dx * dx + dy * dy <= radius * radius;
}

/**
 * 计算原始强度比例
 */
export function calculateRawIntensity(drawIntensity: number, globalIntensity: number): number {
  if (globalIntensity <= 0) return 0;
  const raw = drawIntensity / globalIntensity;
  const clampedRaw = Math.max(0, Math.min(1, raw));
  // 使用配置的精度设置
  return Number(clampedRaw.toFixed(INTENSITY_PRECISION.RAW_RATIO_DECIMAL_PLACES));
}
