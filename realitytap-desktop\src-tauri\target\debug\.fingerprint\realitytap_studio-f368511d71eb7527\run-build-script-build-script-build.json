{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4014864309271236696, "build_script_build", false, 12123301812460887280], [10755362358622467486, "build_script_build", false, 18284492278991896897], [5157003953992891593, "build_script_build", false, 16593853075747243471], [13592916204794590741, "build_script_build", false, 13126635598151438067], [7236291379133587555, "build_script_build", false, 10283187284762382270], [12676100885892732016, "build_script_build", false, 4729103525875833001], [17509843537913359226, "build_script_build", false, 10749617696047832131], [1582828171158827377, "build_script_build", false, 4537830506992277775], [11721252211900136025, "build_script_build", false, 15753928962352390832]], "local": [{"RerunIfChanged": {"output": "debug\\build\\realitytap_studio-f368511d71eb7527\\output", "paths": ["ffmpeg/windows/x64", "libs/windows/x64", "tauri.conf.json", "capabilities", "libgcc_s_seh-1.dll", "librtcore.dll", "librtssl.dll", "librtutils.dll", "libstdc++-6.dll", "libwinpthread-1.dll"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": "{\"$schema\":\"../node_modules/@tauri-apps/cli/config.schema.json\",\"productName\":\"RealityTap Haptics Studio\",\"version\":\"1.0.0\",\"identifier\":\"com.awa.realitytap.desktop\",\"build\":{\"frontendDist\":[\"../dist\",\"../public\"],\"devUrl\":\"http://localhost:5173\",\"beforeDevCommand\":\"npm run dev\",\"beforeBuildCommand\":\"npx vite build\"},\"app\":{\"withGlobalTauri\":true,\"windows\":[{\"title\":\"AWA RealityTap Studio\",\"width\":1280,\"height\":980,\"minWidth\":1280,\"minHeight\":980,\"resizable\":true,\"fullscreen\":false,\"maximized\":false,\"center\":true,\"decorations\":false,\"dragDropEnabled\":false}],\"security\":{\"csp\":null}},\"bundle\":{\"active\":true,\"targets\":\"msi\",\"icon\":[\"icons/icon.icns\",\"icons/icon.ico\"],\"resources\":[\"*.dll\"]},\"plugins\":{\"updater\":{\"active\":true,\"endpoints\":[\"http://localhost:3000/api/v1/updates/{{target}}/{{current_version}}\"],\"dialog\":false,\"dangerousInsecureTransportProtocol\":true,\"pubkey\":\"dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDZBMjY3MEU2RTcwQjIyMEUKUldRT0lndm41bkFtYWhMUXhWSTI3K2haNVBLMEd3dXJGb1F1ZVdmaEdjUG4vV3MzaVJkNWVpR3oK\",\"windows\":{\"installMode\":\"passive\"}}}}"}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}