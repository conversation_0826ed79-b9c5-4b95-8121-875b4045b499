// Group management commands
use crate::{
    commands::app_data::add_to_recent_projects,
    error::{Error, Result},
    models,
    project::{self, HAPTICS_DIR_NAME, io::{AUDIO_DIR_NAME, VIDEO_DIR_NAME}},
    utils::validation::validate_group_name,
};
use chrono::Utc;
use std::fs;
use std::path::PathBuf;
use tauri;
use uuid::Uuid;

#[tauri::command]
pub async fn create_group(
    project_dir_path: String,
    new_group_name: String,
    parent_group_uuid_str: Option<String>,
) -> Result<models::Project> {
    log::info!(
        "Attempting to create group '{}' in project '{}', parent UUID: {:?}",
        new_group_name,
        project_dir_path,
        parent_group_uuid_str
    );

    let proj_dir_path_buf = PathBuf::from(&project_dir_path);
    let mut project = project::read_project_directory(&proj_dir_path_buf)?;

    let parent_path_segment: String = match parent_group_uuid_str.as_ref() {
        Some(uuid_str) => {
            if uuid_str.trim().is_empty() {
                return Err(Error::ValidationError(
                    "Parent group UUID string cannot be empty if provided".to_string(),
                ));
            }
            let parent_uuid = Uuid::parse_str(uuid_str).map_err(|_| {
                Error::ValidationError("Invalid parent group UUID format".to_string())
            })?;
            project
                .groups
                .iter()
                .find(|g| g.group_uuid == parent_uuid)
                .map(|g| g.path.clone())
                .ok_or_else(|| {
                    Error::NotFound(format!("Parent group with UUID {} not found", parent_uuid))
                })?
        }
        None => String::new(), // Root level group
    };

    let validated_group_name = validate_group_name(&new_group_name)?;

    let new_group_logical_path = if parent_path_segment.is_empty() {
        validated_group_name.clone()
    } else {
        format!("{}/{}", parent_path_segment, validated_group_name)
    };

    // Parse parent_group_uuid_str to Option<Uuid>
    let parent_uuid_opt: Option<Uuid> = match &parent_group_uuid_str {
        Some(uuid_str) => {
            if uuid_str.trim().is_empty() {
                None
            } else {
                Uuid::parse_str(uuid_str).ok()
            }
        }
        None => None,
    };

    if project
        .groups
        .iter()
        .any(|g| g.path == new_group_logical_path)
    {
        return Err(Error::ValidationError(format!(
            "A group with the path '{}' already exists.",
            new_group_logical_path
        )));
    }

    let fs_group_path = proj_dir_path_buf
        .join(HAPTICS_DIR_NAME)
        .join(&new_group_logical_path);

    fs::create_dir_all(&fs_group_path).map_err(|e| {
        Error::Io(format!(
            "Failed to create directory {:?}: {}",
            fs_group_path, e
        ))
    })?;
    log::info!("Created directory: {:?}", fs_group_path);

    let new_group = models::Group {
        group_uuid: Uuid::new_v4(),
        name: validated_group_name,
        path: new_group_logical_path,
        description: String::new(),
        parent_group_uuid: parent_uuid_opt,
    };

    project.groups.push(new_group);
    project.last_modified_time = Utc::now();

    project::write_project_directory(&project, &proj_dir_path_buf)?;

    Ok(project)
}

// Helper function to recursively find all descendant group UUIDs and file UUIDs
fn get_all_descendants(
    project: &models::Project,
    parent_group_uuid: Uuid,
    all_groups: &mut Vec<Uuid>,
    all_files: &mut Vec<Uuid>,
) {
    // Find direct children groups of the current parent_group_uuid
    let direct_children_groups: Vec<Uuid> = project
        .groups
        .iter()
        .filter(|g| g.parent_group_uuid == Some(parent_group_uuid))
        .map(|g| g.group_uuid)
        .collect();

    for child_group_uuid in direct_children_groups {
        if !all_groups.contains(&child_group_uuid) {
            all_groups.push(child_group_uuid);
            // Recursively call for grandchildren
            get_all_descendants(project, child_group_uuid, all_groups, all_files);
        }
    }

    // Find files directly within the current parent_group_uuid
    let files_in_group: Vec<Uuid> = project
        .files
        .iter()
        .filter(|f| f.group == Some(parent_group_uuid))
        .map(|f| f.file_uuid)
        .collect();
    for file_uuid in files_in_group {
        if !all_files.contains(&file_uuid) {
            all_files.push(file_uuid);
        }
    }
}

#[tauri::command]
pub async fn remove_group_command(
    project_dir_path: String,
    group_uuid_str: String,
    deletion_mode: Option<String>, // "restrict" or "cascade"
) -> Result<models::Project> {
    log::info!(
        "Attempting to remove group UUID '{}' from project '{}', mode: {:?}",
        group_uuid_str,
        project_dir_path,
        deletion_mode
    );

    let proj_dir_path_buf = PathBuf::from(&project_dir_path);
    let mut project = project::read_project_directory(&proj_dir_path_buf)?;

    let group_to_remove_uuid = Uuid::parse_str(&group_uuid_str)
        .map_err(|_| Error::ValidationError("Invalid group UUID format".to_string()))?;

    // Find the group to remove
    let group_to_remove = project
        .groups
        .iter()
        .find(|g| g.group_uuid == group_to_remove_uuid)
        .cloned();

    if group_to_remove.is_none() {
        return Err(Error::NotFound(format!(
            "Group with UUID '{}' not found.",
            group_uuid_str
        )));
    }

    // Collect all descendants
    let mut descendant_group_uuids: Vec<Uuid> = Vec::new();
    let mut files_in_target_and_descendants: Vec<Uuid> = Vec::new();

    get_all_descendants(
        &project,
        group_to_remove_uuid,
        &mut descendant_group_uuids,
        &mut files_in_target_and_descendants,
    );

    // Add files directly in the target group to the list
    let files_directly_in_target_group: Vec<Uuid> = project
        .files
        .iter()
        .filter(|f| f.group == Some(group_to_remove_uuid))
        .map(|f| f.file_uuid)
        .collect();
    for file_uuid in files_directly_in_target_group {
        if !files_in_target_and_descendants.contains(&file_uuid) {
            files_in_target_and_descendants.push(file_uuid);
        }
    }

    // Handle Deletion Mode
    let mode = deletion_mode.unwrap_or_else(|| "restrict".to_string());

    if mode == "restrict" {
        if !descendant_group_uuids.is_empty() || !files_in_target_and_descendants.is_empty() {
            let mut error_message = "Group is not empty.".to_string();
            if !descendant_group_uuids.is_empty() {
                error_message.push_str(&format!(
                    " Contains {} sub-group(s).",
                    descendant_group_uuids.len()
                ));
            }
            if !files_in_target_and_descendants.is_empty() {
                error_message.push_str(&format!(
                    " Contains {} file(s).",
                    files_in_target_and_descendants.len()
                ));
            }
            error_message.push_str(" Cannot delete in 'restrict' mode.");
            return Err(Error::ValidationError(error_message));
        }
    }

    // Perform Deletion (applies to "cascade", or "restrict" if empty)

    // 1. Remove files from filesystem and project.json
    let mut files_to_keep: Vec<models::HapticFile> = Vec::new();
    let haptics_base_dir = proj_dir_path_buf.join(HAPTICS_DIR_NAME);

    for file_entry in project.files.iter() {
        if files_in_target_and_descendants.contains(&file_entry.file_uuid) {
            // This file is marked for deletion
            let physical_file_path = proj_dir_path_buf.join(&file_entry.path);
            if physical_file_path.exists() {
                log::info!("Cascading delete of file: {:?}", physical_file_path);
                if let Err(e) = fs::remove_file(&physical_file_path) {
                    log::error!(
                        "Failed to delete physical file {:?}: {}",
                        physical_file_path,
                        e
                    );
                }
            } else {
                log::warn!(
                    "Physical file for deletion not found (already removed or inconsistent?): {:?}",
                    physical_file_path
                );
            }

            // Delete associated audio file if exists
            if let Some(audio_rel_path_str) = &file_entry.associated_audio {
                let audio_file_rel_path = PathBuf::from(AUDIO_DIR_NAME).join(audio_rel_path_str);
                let associated_audio_path = proj_dir_path_buf.join(audio_file_rel_path);
                if associated_audio_path.exists() {
                    log::info!("Cascading delete of associated audio file: {:?}", associated_audio_path);
                    if let Err(e) = fs::remove_file(&associated_audio_path) {
                        log::error!(
                            "Failed to delete associated audio file {:?}: {}",
                            associated_audio_path,
                            e
                        );
                    }
                } else {
                    log::warn!(
                        "Associated audio file for deletion not found (already removed?): {:?}",
                        associated_audio_path
                    );
                }
            }

            // Delete associated video file if exists
            if let Some(video_rel_path_str) = &file_entry.associated_video {
                let video_file_rel_path = PathBuf::from(VIDEO_DIR_NAME).join(video_rel_path_str);
                let associated_video_path = proj_dir_path_buf.join(video_file_rel_path);
                if associated_video_path.exists() {
                    log::info!("Cascading delete of associated video file: {:?}", associated_video_path);
                    if let Err(e) = fs::remove_file(&associated_video_path) {
                        log::error!(
                            "Failed to delete associated video file {:?}: {}",
                            associated_video_path,
                            e
                        );
                    }
                } else {
                    log::warn!(
                        "Associated video file for deletion not found (already removed?): {:?}",
                        associated_video_path
                    );
                }
            }
        } else {
            files_to_keep.push(file_entry.clone());
        }
    }
    project.files = files_to_keep;

    // 2. Remove groups from filesystem and project.json
    let all_groups_to_remove_uuids =
        [vec![group_to_remove_uuid], descendant_group_uuids.clone()].concat();

    // Sort groups by path length in descending order to delete deepest first
    let mut groups_to_remove_sorted: Vec<models::Group> = project
        .groups
        .iter()
        .filter(|g| all_groups_to_remove_uuids.contains(&g.group_uuid))
        .cloned()
        .collect();

    groups_to_remove_sorted.sort_by(|a, b| b.path.len().cmp(&a.path.len()));

    for group_info in groups_to_remove_sorted.iter() {
        let physical_group_dir_path = haptics_base_dir.join(&group_info.path);
        if physical_group_dir_path.exists() {
            log::info!(
                "Cascading delete of directory: {:?}",
                physical_group_dir_path
            );
            if let Err(e) = fs::remove_dir_all(&physical_group_dir_path) {
                log::error!(
                    "Failed to delete physical directory {:?}: {}",
                    physical_group_dir_path,
                    e
                );
            }
        } else {
            log::warn!(
                "Physical directory for group deletion not found: {:?}",
                physical_group_dir_path
            );
        }
    }

    project
        .groups
        .retain(|g| !all_groups_to_remove_uuids.contains(&g.group_uuid));

    project.last_modified_time = Utc::now();
    project::write_project_directory(&project, &proj_dir_path_buf)?;
    add_to_recent_projects(&project, &project_dir_path)?;

    log::info!(
        "Successfully removed group UUID '{}' and its contents (if any, in cascade mode).",
        group_uuid_str
    );
    Ok(project)
}

#[tauri::command]
pub async fn rename_group_command(
    project_dir_path: String,
    group_uuid_str: String,
    new_group_name: String,
) -> Result<models::Project> {
    log::info!(
        "Attempting to rename group UUID '{}' to '{}' in project '{}'",
        group_uuid_str,
        new_group_name,
        project_dir_path
    );

    let proj_dir_path_buf = PathBuf::from(&project_dir_path);
    let mut project = project::read_project_directory(&proj_dir_path_buf)?;

    let group_uuid_to_rename = Uuid::parse_str(&group_uuid_str)
        .map_err(|_| Error::ValidationError("无效的分组UUID格式".to_string()))?;

    let group_to_rename_idx = project
        .groups
        .iter()
        .position(|g| g.group_uuid == group_uuid_to_rename)
        .ok_or_else(|| Error::NotFound(format!("未找到UUID为 {} 的分组", group_uuid_str)))?;

    let old_group_name = project.groups[group_to_rename_idx].name.clone();
    let old_logical_path = project.groups[group_to_rename_idx].path.clone();
    let parent_uuid_of_renamed_group = project.groups[group_to_rename_idx].parent_group_uuid;

    let validated_name = validate_group_name(&new_group_name)?;

    if validated_name == old_group_name {
        log::info!("分组名称未更改，跳过重命名。");
        return Ok(project);
    }

    // Determine the parent path segment for constructing the new logical path
    let parent_path_segment = match parent_uuid_of_renamed_group {
        Some(p_uuid) => project
            .groups
            .iter()
            .find(|g| g.group_uuid == p_uuid)
            .map(|g| g.path.clone())
            .unwrap_or_else(String::new),
        None => String::new(), // Root group
    };

    let new_logical_path = if parent_path_segment.is_empty() {
        validated_name.clone()
    } else {
        format!("{}/{}", parent_path_segment, validated_name)
    };

    // Check for name collision at the same level
    if project.groups.iter().any(|g| {
        g.group_uuid != group_uuid_to_rename
            && g.parent_group_uuid == parent_uuid_of_renamed_group
            && g.name == validated_name
    }) {
        return Err(Error::ValidationError(format!(
            "在同一父分组下已存在名为 '{}' 的分组",
            validated_name
        )));
    }

    // Check for full path collision
    if project
        .groups
        .iter()
        .any(|g| g.group_uuid != group_uuid_to_rename && g.path == new_logical_path)
    {
        return Err(Error::ValidationError(format!(
            "已存在路径为 '{}' 的分组",
            new_logical_path
        )));
    }

    // File System Rename
    let haptics_dir_name = HAPTICS_DIR_NAME;
    let old_physical_path = proj_dir_path_buf
        .join(haptics_dir_name)
        .join(&old_logical_path);
    let new_physical_path = proj_dir_path_buf
        .join(haptics_dir_name)
        .join(&new_logical_path);

    if old_physical_path.exists() {
        if let Err(e) = fs::rename(&old_physical_path, &new_physical_path) {
            log::error!(
                "重命名物理目录失败: 从 {:?} 到 {:?}: {}",
                old_physical_path,
                new_physical_path,
                e
            );
            return Err(Error::Io(format!("重命名物理目录失败: {}", e)));
        }
        log::info!(
            "重命名物理目录: {:?} -> {:?}",
            old_physical_path,
            new_physical_path
        );
    } else {
        log::warn!(
            "旧的物理目录 {:?} 不存在，可能仅为逻辑分组或之前已重命名/删除。",
            old_physical_path
        );
    }

    // Update Project Metadata
    project.groups[group_to_rename_idx].name = validated_name.clone();
    project.groups[group_to_rename_idx].path = new_logical_path.clone();

    // Update paths of subgroups
    let old_path_prefix_for_children = format!("{}/", old_logical_path);
    let new_path_prefix_for_children = format!("{}/", new_logical_path);

    for group in project.groups.iter_mut() {
        if group.group_uuid != group_uuid_to_rename
            && group.path.starts_with(&old_path_prefix_for_children)
        {
            group.path = group.path.replacen(
                &old_path_prefix_for_children,
                &new_path_prefix_for_children,
                1,
            );
        }
    }

    // Update paths of files within the renamed group and its subgroups
    for file_entry in project.files.iter_mut() {
        if file_entry.path.starts_with(&old_logical_path) {
            if file_entry.path == old_logical_path {
                file_entry.path = new_logical_path.clone();
            } else if file_entry.path.starts_with(&old_path_prefix_for_children) {
                file_entry.path = file_entry.path.replacen(
                    &old_path_prefix_for_children,
                    &new_path_prefix_for_children,
                    1,
                );
            } else {
                let path_parts: Vec<&str> = file_entry.path.splitn(2, '/').collect();
                if path_parts.len() > 0 && path_parts[0] == old_logical_path {
                    if path_parts.len() > 1 {
                        file_entry.path = format!("{}/{}", new_logical_path, path_parts[1]);
                    } else {
                        file_entry.path = new_logical_path.clone();
                    }
                }
            }
        }
    }

    project.last_modified_time = Utc::now();

    project::write_project_directory(&project, &proj_dir_path_buf)?;
    add_to_recent_projects(&project, &project_dir_path)?;

    log::info!(
        "分组 {} ({}) 已成功重命名为 {} ({})",
        old_group_name,
        old_logical_path,
        validated_name,
        new_logical_path
    );
    Ok(project)
}
