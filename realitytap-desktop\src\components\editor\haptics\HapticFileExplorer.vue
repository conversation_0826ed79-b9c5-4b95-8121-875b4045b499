<template>
  <div class="haptics-panel haptic-file-explorer">
    <n-spin :show="isImportingAudio" :description="importSpinMessage" size="large">
      <div class="haptics-header">
        <h2 class="panel-title">{{ t('editor.hapticFiles.title') }}</h2>
      </div>

      <div
        class="haptics-list"
        @contextmenu.prevent="handlePanelItselfContextMenu"
        @dragenter="onRootDropEnter"
        @dragover="onRootDropOver"
        @drop="onRootDrop"
      >
        <!-- Naive UI Tree 用于显示文件和分组 -->
        <n-tree
          block-line
          selectable
          draggable
          expand-on-dragenter
          virtual-scroll
          :data="treeData"
          :selected-keys="selectedTreeKeys"
          @update:selected-keys="handleTreeSelect"
          :expanded-keys="expandedTreeKeys"
          @update:expanded-keys="handleExpandedKeysUpdate"
          :node-props="getNodeProps"
          :render-switcher-icon="renderSwitcherIcon"
          :render-label="renderLabel"
          :animated="false"
          @drop="handleTreeDrop"
          @dragstart="handleTreeNodeDragStart"
          @dragend="handleTreeNodeDragEnd"
        />
      </div>
    </n-spin>
  </div>

  <n-dropdown
    placement="bottom-start"
    trigger="manual"
    :x="dropdownX"
    :y="dropdownY"
    :options="dropdownOptions"
    v-model:show="showDropdown"
    @select="handleDropdownSelect"
    :on-clickoutside="() => (showDropdown = false)"
  />

  <!-- 播放效果对话框 -->
  <PlayEffectDialog
    v-model:visible="showPlayEffectDialog"
    :events="playEffectEvents"
    :current-file="transformedCurrentFile"
    :is-loading="playEffectIsLoading"
    :error="playEffectError"
  />
</template>

<script setup lang="ts">
import { useProjectStore } from "@/stores/haptics-project-store";
import type { HapticFile, HapticsGroup } from "@/types/haptic-project";
import { NDropdown, NSpin, NTree, useMessage, type TreeOption as NaiveTreeOption } from "naive-ui";
import { computed, ref, watch, nextTick } from "vue";

export type ContextMenuType = "file" | "group" | "panel";

export interface ContextMenuEventPayload {
  type: ContextMenuType;
  event: MouseEvent;
  item?: HapticFile | HapticsGroup;
}

// 导入工具函数和 composables
import { findNodeByKey, getItemFromNodeKey } from "@/utils/tree/treeUtils";
import { useTreeDataBuilder } from "@/composables/tree/useTreeDataBuilder";
import { useInlineEdit } from "@/composables/tree/useInlineEdit";
import { useContextMenu } from "@/composables/tree/useContextMenu";
import { useContextMenuOperations } from "@/composables/tree/useContextMenuOperations";
import { useDragAndDrop } from "@/composables/tree/useDragAndDrop";
import { useTreeRenderer } from "@/composables/tree/useTreeRenderer";
import { useTreeState } from "@/composables/tree/useTreeState";
import { useTreeInteraction } from "@/composables/tree/useTreeInteraction";
import { useI18n } from "@/composables/useI18n";
import { usePlayEffect } from "@/composables/haptics/usePlayEffect";
import PlayEffectDialog from "./PlayEffectDialog.vue";

const projectStore = useProjectStore();
const message = useMessage();
const { t } = useI18n();

// 播放效果功能
const {
  showDialog: showPlayEffectDialog,
  events: playEffectEvents,
  currentFile: playEffectCurrentFile,
  isLoading: playEffectIsLoading,
  error: playEffectError,
  playEffect
} = usePlayEffect();

// 转换 currentFile 格式以匹配 PlayEffectDialog 的类型要求
const transformedCurrentFile = computed(() => {
  if (!playEffectCurrentFile.value) {
    return null;
  }
  return {
    name: playEffectCurrentFile.value.name,
    uuid: playEffectCurrentFile.value.fileUuid,
    path: playEffectCurrentFile.value.path
  };
});

const emit = defineEmits<{
  (e: "select-project-item", item: HapticFile | HapticsGroup | null): void;
}>();

// 先声明新建分组的状态变量
const isCreatingNewGroup = ref(false);
const newGroupParentUuid = ref<string | null>(null);
const newGroupName = ref("");
const newGroupTemporaryKey = ref<string | null>(null);

// 使用树形数据构建器
const { treeData } = useTreeDataBuilder(
  computed(() => projectStore.activeProject),
  isCreatingNewGroup,
  newGroupTemporaryKey,
  newGroupName,
  newGroupParentUuid
);

// 使用内联编辑功能
const {
  // 编辑状态
  editingNodeKey,
  inlineEditValue,
  inputRef,

  // 新建分组功能
  triggerNewGroupCreation,
  cancelNewGroupCreation,
  commitNewGroup,

  // 分组编辑
  triggerGroupRename,
  commitGroupRename,
  cancelGroupRename,

  // 文件编辑
  triggerFileRename,
  commitFileRename,
  cancelFileRename,

  // 通用功能
  cancelAnyOngoingEdit,
} = useInlineEdit(
  projectStore,
  treeData,
  ref([]),
  // 传递新建分组的状态变量
  isCreatingNewGroup,
  newGroupParentUuid,
  newGroupName,
  newGroupTemporaryKey,
  t
);

// 使用树形状态管理
const {
  selectedTreeKeys,
  expandedTreeKeys,
  ensureParentGroupsExpanded,
  selectProjectItem,
  handleTreeSelect,
  getBaseNodeProps,
  handleExpandedKeysUpdate,
} = useTreeState(projectStore, treeData, triggerFileRename, emit);

// 使用树形交互处理
const { handleDeleteGroup } = useTreeInteraction(projectStore, selectedTreeKeys, emit, cancelAnyOngoingEdit, t);

// 声明右键点击的节点键（需要在 useTreeRenderer 之前声明）
const rightClickedNodeKey = ref<string | null>(null);

// 使用树形渲染器
const { renderLabel, renderSwitcherIcon } = useTreeRenderer(
  projectStore,
  editingNodeKey,
  inlineEditValue,
  inputRef,
  isCreatingNewGroup,
  newGroupTemporaryKey,
  newGroupName,
  commitNewGroup,
  cancelNewGroupCreation,
  commitGroupRename,
  cancelGroupRename,
  commitFileRename,
  cancelFileRename,
  message,
  rightClickedNodeKey,
  t
);

// 添加 inputRef 焦点管理 watcher（重构时被遗漏的关键逻辑）
watch(inputRef, (newInputInstance) => {
  if (newInputInstance) {
    nextTick(() => {
      // 确保 DOM 已更新
      newInputInstance.focus();
    });
  }
});

// 初始化 composables
const {
  showDropdown,
  dropdownX,
  dropdownY,
  dropdownOptions,
  currentItem,
  currentType,
  isImportingAudio,
  importSpinMessage,
  handleShowContextMenu,
  handlePanelItselfContextMenu,
} = useContextMenu(expandedTreeKeys, ensureParentGroupsExpanded, t);

const { createDropdownSelectHandler } = useContextMenuOperations(
  projectStore,
  expandedTreeKeys,
  selectedTreeKeys,
  ensureParentGroupsExpanded,
  triggerNewGroupCreation,
  triggerGroupRename,
  triggerFileRename,
  handleDeleteGroup,
  findNodeByKey,
  treeData,
  getItemFromNodeKey,
  selectProjectItem,
  emit,
  t,
  playEffect
);

// 创建节点属性获取函数，结合状态管理和上下文菜单
const getNodeProps = (nodeOption: { option: NaiveTreeOption }) => {
  const key = String(nodeOption.option.key);
  const baseProps = getBaseNodeProps(nodeOption);

  return {
    ...baseProps,
    onContextmenu: (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      rightClickedNodeKey.value = key;

      // 获取对应的项目数据并显示右键菜单
      if (projectStore.activeProject) {
        const item = getItemFromNodeKey(key, projectStore.activeProject);
        if (item) {
          // 确定菜单类型
          const menuType: ContextMenuType = key.startsWith('group-') ? 'group' : 'file';

          // 显示右键菜单
          handleShowContextMenu({
            type: menuType,
            event: e,
            item: item
          });
        }
      }
    },
  };
};

// 创建 handleDropdownSelect 函数
const handleDropdownSelect = createDropdownSelectHandler(currentType, currentItem, showDropdown);

const { handleTreeDrop, handleTreeNodeDragStart, handleTreeNodeDragEnd, onRootDropEnter, onRootDropOver, onRootDrop } = useDragAndDrop(
  projectStore,
  expandedTreeKeys,
  selectedTreeKeys,
  findNodeByKey,
  treeData,
  t
);
</script>

<style scoped>
.haptics-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.haptics-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 1em;
  height: 60px;
  min-height: 60px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.panel-title {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
  color: #d3d3c8;
  letter-spacing: 0.5px;
  font-family: "Inter", "Roboto", -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  padding-left: 2px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  transition: color 0.3s ease;
}

.haptics-list {
  flex: 1;
  height: 100%;
  padding-bottom: 1rem;
}

/* 文件树自定义样式 */
:deep(.v-vl) {
  --n-node-color-hover: #6c7174d0 !important;
  --n-node-color-pressed: #b5babead !important;
}

:deep(.n-tree-node-content) {
  padding: 8px 10px;
  color: rgba(255, 255, 255, 0.85);
}

/* 组节点标签加粗 */
:deep(.n-tree-node[data-key^="group-"] > .n-tree-node-content .tree-node-label) {
  font-weight: 500;
}

:deep(.n-tree-node-switcher) {
  margin-top: 5px;
}

:deep(.n-tree-node-content__text .inline-edit-input.n-input) {
  margin-top: -4px;
  margin-bottom: -4px;
}

.switcher-has-children {
  color: #16f57a;
  opacity: 1;
  transition: color 0.2s, opacity 0.2s;
}
.switcher-has-children:hover {
  color: #16f57a8c;
}
.switcher-no-children {
  color: #bbb;
  opacity: 0.5;
  cursor: default;
  transition: color 0.2s, opacity 0.2s;
}
.switcher-no-children:hover {
  color: #bbb;
  opacity: 0.5;
}



.right-click-indicator {
  background-color: #ff0000 !important; /* 使用一个非常显眼的颜色 */
}

/* 未保存状态指示器样式 */
.file-label {
  display: inline-flex;
  align-items: center;
  position: relative;
  padding: 0;
  border-radius: 4px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.file-label--unsaved {
  background: linear-gradient(90deg, rgba(255, 71, 87, 0.15) 0%, rgba(255, 107, 107, 0.1) 100%) !important;
  border-left: 3px solid rgba(255, 71, 87, 0.6) !important;
  border-radius: 5px !important;
  padding-left: 3px !important;
  box-shadow: 0 0 6px rgba(255, 71, 87, 0.15) !important;
  animation: unsaved-glow 2s infinite !important;
}

/* 确保在 Naive UI Tree 组件内部也能正确应用样式 */
:deep(.n-tree-node-content .file-label--unsaved) {
  background: linear-gradient(90deg, rgba(255, 71, 87, 0.15) 0%, rgba(255, 107, 107, 0.1) 100%) !important;
  border-left: 3px solid rgba(255, 71, 87, 0.6) !important;
  border-radius: 5px !important;
  padding-left: 3px !important;
  box-shadow: 0 0 6px rgba(255, 71, 87, 0.15) !important;
  animation: unsaved-glow 2s infinite !important;
}

@keyframes unsaved-glow {
  0%,
  100% {
    box-shadow: 0 0 6px rgba(255, 71, 87, 0.15);
    border-left-color: rgba(255, 71, 87, 0.6);
  }
  50% {
    box-shadow: 0 0 10px rgba(255, 71, 87, 0.3);
    border-left-color: rgba(255, 71, 87, 0.8);
  }
}
</style>
