/**
 * RealityTap V2 格式转换器
 * 基于 RealityTapEffectV2.java 的 convertToArray 实现
 */

import type { 
  ConvertToArrayResult,
  ConvertToArrayOptions,
  ConvertToArrayMetadata
} from '@/types/reality-tap-converter';
import {
  V2_CONSTANTS,
  EVENT_TYPES,
  EVENT_TYPE_STRINGS
} from '@/types/reality-tap-converter';
import type { RenderableEvent } from '@/types/haptic-editor';
import type { RealityTapEffectV2 } from '@/types/haptic-file';
import { logger, LogModule } from '@/utils/logger/logger';

/**
 * V2 格式的 PatternListItem 接口（内部使用）
 */
interface V2PatternListItem {
  patternIndex: number;
  absoluteTime: number;
  events: any[];
}

/**
 * 将 V2 格式数据转换为数组
 * @param input V2 格式的输入数据
 * @param options 转换选项
 * @returns 转换结果
 */
export function convertV2ToArray(
  input: RealityTapEffectV2 | RenderableEvent[],
  options: ConvertToArrayOptions = {}
): ConvertToArrayResult {
  const startTime = performance.now();
  
  try {
    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, '[V2Converter] 开始 V2 转换', {
        inputType: Array.isArray(input) ? 'RenderableEvent[]' : 'RealityTapEffectV2',
        processId: options.processId,
        sequence: options.sequence
      });
    }

    // 提取 PatternList 数据
    const patternList = extractPatternListFromV2Input(input, options);
    if (!patternList || patternList.length === 0) {
      const errorMsg = '没有找到有效的 PatternList 数据';

      if (options.enableLogging) {
        logger.error(LogModule.WAVEFORM, '[V2Converter] PatternList 数据提取失败', {
          inputType: Array.isArray(input) ? 'RenderableEvent[]' : 'RealityTapEffectV2',
          hasPatternList: !!(input as any)?.PatternList,
          patternListLength: Array.isArray((input as any)?.PatternList) ? (input as any).PatternList.length : 0,
          isInputArray: Array.isArray(input),
          inputLength: Array.isArray(input) ? input.length : 0
        });
      }

      return {
        success: false,
        error: errorMsg,
        version: 2
      };
    }

    if (options.enableLogging) {
      logger.info(LogModule.WAVEFORM, '[V2Converter] PatternList 数据提取成功', {
        patternListCount: patternList.length,
        totalEvents: patternList.reduce((sum: number, item: any) => sum + item.events.length, 0),
        inputType: Array.isArray(input) ? 'RenderableEvent[]' : 'RealityTapEffectV2'
      });
    }

    // 预先计算数组大小
    const arraySize = calculateV2ArraySize(patternList);
    const array = new Array(arraySize).fill(0);

    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, '[V2Converter] 初始化输出数组', {
        arraySize,
        patternListCount: patternList.length,
        totalEvents: patternList.reduce((sum: number, item: any) => sum + item.events.length, 0),
        memoryUsage: `${(arraySize * 8 / 1024).toFixed(2)} KB`
      });
    }

    // 写入头部信息
    let index = 0;
    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, '[V2Converter] 写入头部信息', {
        headerSize: V2_CONSTANTS.HEADER_SIZE,
        patternListCount: patternList.length,
        processId: options.processId,
        sequence: options.sequence
      });
    }

    index = writeV2Header(array, index, patternList.length, options);

    // 写入 PatternList 数据
    let processedEvents = 0;
    let skippedEvents = 0;
    const warnings: string[] = [];

    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, '[V2Converter] 开始处理 PatternList', {
        patternListCount: patternList.length,
        currentIndex: index
      });
    }

    for (let i = 0; i < patternList.length; i++) {
      try {
        const patternItem = patternList[i];

        if (options.enableLogging && (i < 3 || i % 10 === 0 || i === patternList.length - 1)) {
          logger.debug(LogModule.WAVEFORM, '[V2Converter] 处理 PatternListItem', {
            itemIndex: i,
            absoluteTime: patternItem.absoluteTime,
            eventCount: patternItem.events.length,
            progress: `${i + 1}/${patternList.length}`,
            currentIndex: index
          });
        }

        const result = writeV2PatternListItem(array, index, patternItem, i, options);

        index = result.nextIndex;
        processedEvents += result.processedEvents;
        skippedEvents += result.skippedEvents;

        if (result.warnings) {
          warnings.push(...result.warnings);
        }
      } catch (error) {
        const errorMsg = `PatternListItem[${i}]处理失败: ${error instanceof Error ? error.message : String(error)}`;
        warnings.push(errorMsg);

        if (options.enableLogging) {
          logger.warn(LogModule.WAVEFORM, '[V2Converter] PatternListItem处理异常', {
            itemIndex: i,
            error: error instanceof Error ? error.message : String(error),
            currentIndex: index
          });
        }
      }
    }

    if (options.enableLogging) {
      logger.info(LogModule.WAVEFORM, '[V2Converter] PatternList 处理完成', {
        processedEvents,
        skippedEvents,
        totalPatternItems: patternList.length,
        successRate: `${((processedEvents / (processedEvents + skippedEvents || 1)) * 100).toFixed(1)}%`,
        warningCount: warnings.length,
        finalIndex: index
      });
    }

    const processingTime = performance.now() - startTime;
    
    const metadata: ConvertToArrayMetadata = {
      totalEvents: processedEvents,
      arrayLength: arraySize,
      processingTime,
      detectedVersion: 2,
      patternListCount: patternList.length,
      skippedEvents: skippedEvents > 0 ? skippedEvents : undefined
    };

    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, '[V2Converter] V2 转换完成', {
        processedEvents,
        skippedEvents,
        patternListCount: patternList.length,
        arraySize,
        processingTime: `${processingTime.toFixed(2)}ms`
      });
    }

    return {
      success: true,
      data: array,
      version: 2,
      metadata,
      warnings: warnings.length > 0 ? warnings : undefined
    };

  } catch (error) {
    const errorMessage = `V2 转换失败: ${error instanceof Error ? error.message : String(error)}`;
    
    if (options.enableLogging) {
      logger.error(LogModule.WAVEFORM, '[V2Converter] 转换过程异常', error);
    }

    return {
      success: false,
      error: errorMessage,
      version: 2
    };
  }
}

/**
 * 从 V2 输入中提取 PatternList 数据
 */
function extractPatternListFromV2Input(
  input: RealityTapEffectV2 | RenderableEvent[],
  _options: ConvertToArrayOptions
): V2PatternListItem[] | null {
  try {
    if (Array.isArray(input)) {
      // 将 RenderableEvent[] 转换为单个 PatternListItem
      return [{
        patternIndex: 0,
        absoluteTime: 0,
        events: input
      }];
    }

    // 从 RealityTapEffectV2 中提取 PatternList
    const patternList = input.PatternList;
    if (!patternList || !Array.isArray(patternList)) {
      return null;
    }

    return patternList.map((item: any, index: number) => ({
      patternIndex: index,
      absoluteTime: item.AbsoluteTime ?? 0,
      events: (item.Pattern ?? []).map((wrappedEvent: any) =>
        wrappedEvent.Event || wrappedEvent
      )
    }));

  } catch (error) {
    return null;
  }
}

/**
 * 计算 V2 数组大小
 */
function calculateV2ArraySize(patternList: V2PatternListItem[]): number {
  let totalSize = V2_CONSTANTS.HEADER_SIZE; // 头部固定5个元素

  for (const patternItem of patternList) {
    totalSize += V2_CONSTANTS.PATTERN_LIST_ITEM_FIELDS; // pattern list 每项固定3个元素
    
    for (const event of patternItem.events) {
      totalSize += V2_CONSTANTS.EVENT_HEADER_FIELDS; // event type 和 length
      totalSize += getV2EventLength(event);
    }
  }

  return totalSize;
}

/**
 * 获取 V2 事件长度
 */
function getV2EventLength(event: any): number {
  const eventType = normalizeEventType(event);
  
  if (eventType === EVENT_TYPE_STRINGS.TRANSIENT) {
    return V2_CONSTANTS.TRANSIENT_EVENT_LENGTH;
  } else if (eventType === EVENT_TYPE_STRINGS.CONTINUOUS) {
    const curves = extractCurveData(event);
    return V2_CONSTANTS.CURVE_POINT_FIELDS * curves.length + V2_CONSTANTS.CONTINUOUS_BASE_LENGTH;
  }
  
  return 0;
}

/**
 * 写入 V2 头部信息
 */
function writeV2Header(
  array: number[],
  startIndex: number,
  patternCount: number,
  options: ConvertToArrayOptions
): number {
  let index = startIndex;
  
  array[index++] = 2;                           // format version
  array[index++] = 2;                           // metadata version
  array[index++] = options.processId ?? 0;     // process id
  array[index++] = options.sequence ?? 0;      // vibration sequence
  array[index++] = patternCount;               // pattern count
  
  return index;
}

/**
 * 写入 V2 PatternListItem
 */
function writeV2PatternListItem(
  array: number[],
  startIndex: number,
  patternItem: V2PatternListItem,
  itemIndex: number,
  options: ConvertToArrayOptions
): { nextIndex: number; processedEvents: number; skippedEvents: number; warnings?: string[] } {
  let index = startIndex;
  let processedEvents = 0;
  let skippedEvents = 0;
  const warnings: string[] = [];

  // 写入 PatternListItem 头部
  array[index++] = patternItem.patternIndex;    // pattern index
  array[index++] = patternItem.absoluteTime;    // absolute time
  array[index++] = patternItem.events.length;   // event count

  // 写入事件数据
  for (let i = 0; i < patternItem.events.length; i++) {
    try {
      const event = patternItem.events[i];
      const result = writeV2Event(array, index, event, i, options);
      
      if (result.success) {
        index = result.nextIndex;
        processedEvents++;
      } else {
        skippedEvents++;
        if (result.warning) {
          warnings.push(`PatternListItem[${itemIndex}].Event[${i}]: ${result.warning}`);
        }
      }
    } catch (error) {
      skippedEvents++;
      const errorMsg = `Event[${i}]处理失败: ${error instanceof Error ? error.message : String(error)}`;
      warnings.push(`PatternListItem[${itemIndex}]: ${errorMsg}`);
    }
  }

  return {
    nextIndex: index,
    processedEvents,
    skippedEvents,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * 写入 V2 事件
 */
function writeV2Event(
  array: number[],
  startIndex: number,
  event: any,
  _eventIndex: number,
  _options: ConvertToArrayOptions
): { success: boolean; nextIndex: number; warning?: string } {
  try {
    const eventType = normalizeEventType(event);
    if (!eventType) {
      return {
        success: false,
        nextIndex: startIndex,
        warning: `无效的事件类型 "${event.type || event.Type}"`
      };
    }

    let index = startIndex;
    const eventLength = getV2EventLength(event);

    if (eventType === EVENT_TYPE_STRINGS.TRANSIENT) {
      // 写入瞬时事件
      array[index++] = EVENT_TYPES.TRANSIENT;                    // event type
      array[index++] = eventLength;                              // event length
      array[index++] = extractMotorIndex(event);                 // motor index
      array[index++] = extractRelativeTime(event);               // relative time
      array[index++] = extractIntensity(event);                  // intensity
      array[index++] = extractFrequency(event);                  // frequency
      array[index++] = V2_CONSTANTS.TRANSIENT_DURATION;          // duration (fixed 48)
      
    } else if (eventType === EVENT_TYPE_STRINGS.CONTINUOUS) {
      // 写入连续事件
      const curves = extractCurveData(event);
      
      array[index++] = EVENT_TYPES.CONTINUOUS;                   // event type
      array[index++] = eventLength;                              // event length
      array[index++] = extractMotorIndex(event);                 // motor index
      array[index++] = extractRelativeTime(event);               // relative time
      array[index++] = extractIntensity(event);                  // global intensity
      array[index++] = extractFrequency(event);                  // global frequency
      array[index++] = extractDuration(event);                   // duration
      array[index++] = curves.length;                            // curve count

      // 写入曲线数据
      for (const curve of curves) {
        array[index++] = curve.time;                             // curve time
        array[index++] = Math.round(curve.intensity * V2_CONSTANTS.INTENSITY_SCALE_FACTOR); // curve intensity
        array[index++] = curve.frequency;                        // curve frequency
      }
    }

    return {
      success: true,
      nextIndex: index
    };

  } catch (error) {
    return {
      success: false,
      nextIndex: startIndex,
      warning: `处理失败 - ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 标准化事件类型
 */
function normalizeEventType(event: any): string | null {
  const type = event.type || event.Type;
  if (typeof type === 'string') {
    const lowerType = type.toLowerCase();
    if (lowerType === 'transient') return EVENT_TYPE_STRINGS.TRANSIENT;
    if (lowerType === 'continuous') return EVENT_TYPE_STRINGS.CONTINUOUS;
  }
  return null;
}

/**
 * 提取马达索引（V2 专用）
 */
function extractMotorIndex(event: any): number {
  const index = event.Index ?? event.index ?? 0;
  return Math.max(0, Math.min(1, Math.floor(Number(index) || 0)));
}

/**
 * 提取相对时间
 */
function extractRelativeTime(event: any): number {
  const relativeTime = event.RelativeTime ?? event.relativeTime ?? event.startTime ?? 0;
  return Math.max(0, Math.floor(Number(relativeTime) || 0));
}

/**
 * 提取强度值
 */
function extractIntensity(event: any): number {
  const intensity = event.Parameters?.Intensity ?? 
                   event.parameters?.intensity ??
                   event.intensity ??
                   event.eventIntensity ??
                   0;
  
  return Math.max(0, Math.min(100, Math.floor(Number(intensity) || 0)));
}

/**
 * 提取频率值
 */
function extractFrequency(event: any): number {
  const frequency = event.Parameters?.Frequency ?? 
                   event.parameters?.frequency ??
                   event.frequency ??
                   event.eventFrequency ??
                   0;
  
  return Math.max(0, Math.min(100, Math.floor(Number(frequency) || 0)));
}

/**
 * 提取持续时间
 */
function extractDuration(event: any): number {
  const duration = event.Duration ?? event.duration ?? 0;
  return Math.max(0, Math.floor(Number(duration) || 0));
}

/**
 * 提取曲线数据
 */
function extractCurveData(event: any): Array<{ time: number; intensity: number; frequency: number }> {
  try {
    let curves = event.Parameters?.Curves ?? 
                event.parameters?.curves ??
                event.curves ??
                [];

    if (!Array.isArray(curves)) {
      return [];
    }

    return curves.map((curve: any) => ({
      time: Math.max(0, Math.floor(Number(curve.Time ?? curve.time ?? curve.timeOffset ?? 0))),
      intensity: Math.max(0, Math.min(1, Number(curve.Intensity ?? curve.intensity ?? curve.rawIntensity ?? 0))),
      frequency: Math.max(-100, Math.min(100, Math.floor(Number(curve.Frequency ?? curve.frequency ?? curve.relativeCurveFrequency ?? 0))))
    }));

  } catch (error) {
    return [];
  }
}
