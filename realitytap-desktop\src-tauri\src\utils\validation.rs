// Common validation functions
use crate::error::{Error, Result};
use crate::utils::constants::INVALID_FILENAME_CHARS;

/// Validates a project name for file system safety
pub fn validate_project_name(name: &str) -> Result<String> {
    let trimmed = name.trim();
    
    if trimmed.is_empty() {
        return Err(Error::NameInvalid("项目名称不能为空".to_string()));
    }
    
    if trimmed.contains(INVALID_FILENAME_CHARS) {
        return Err(Error::NameInvalid(format!(
            "项目名称包含非法字符: {}",
            trimmed
        )));
    }
    
    Ok(trimmed.to_string())
}

/// Validates a group name for file system safety
pub fn validate_group_name(name: &str) -> Result<String> {
    let trimmed = name.trim();
    
    if trimmed.is_empty() {
        return Err(Error::ValidationError("分组名称不能为空".to_string()));
    }
    
    if trimmed.contains('/') || trimmed.contains('\\') {
        return Err(Error::ValidationError(
            "分组名称不能包含路径分隔符".to_string(),
        ));
    }
    
    if trimmed.contains(&[':', '*', '?', '"', '<', '>', '|']) {
        return Err(Error::ValidationError(format!(
            "分组名称包含非法字符: {}",
            trimmed
        )));
    }
    
    Ok(trimmed.to_string())
}

/// Validates a file name for file system safety with enhanced cross-platform checks
pub fn validate_file_name(name: &str, expected_extension: Option<&str>) -> Result<String> {
    use crate::utils::constants::*;

    let trimmed = name.trim();

    // Check if empty
    if trimmed.is_empty() {
        return Err(Error::ValidationError("文件名不能为空".to_string()));
    }

    // Check length limits
    let max_length = if cfg!(windows) {
        MAX_FILENAME_LENGTH_WINDOWS
    } else {
        MAX_FILENAME_LENGTH_UNIX
    };

    if trimmed.len() > max_length {
        return Err(Error::ValidationError(format!(
            "文件名过长，最大长度为 {} 字符，当前长度为 {} 字符",
            max_length, trimmed.len()
        )));
    }

    // Check for invalid characters
    if trimmed.chars().any(|c| INVALID_FILENAME_CHARS.contains(&c)) {
        return Err(Error::ValidationError(format!(
            "文件名包含非法字符，不允许使用: / \\ : * ? \" < > |"
        )));
    }

    // Check for problematic characters
    if trimmed.chars().any(|c| PROBLEMATIC_FILENAME_CHARS.contains(&c)) {
        return Err(Error::ValidationError(
            "文件名包含控制字符或换行符".to_string()
        ));
    }

    // Check for control characters
    if trimmed.chars().any(|c| {
        let code = c as u32;
        CONTROL_CHAR_RANGES.iter().any(|(start, end)| code >= *start && code <= *end)
    }) {
        return Err(Error::ValidationError(
            "文件名包含控制字符".to_string()
        ));
    }

    // Check for Windows reserved names (case-insensitive)
    if cfg!(windows) {
        let name_without_ext = std::path::Path::new(trimmed)
            .file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or(trimmed);

        if WINDOWS_RESERVED_NAMES.iter().any(|&reserved|
            name_without_ext.eq_ignore_ascii_case(reserved)
        ) {
            return Err(Error::ValidationError(format!(
                "文件名 '{}' 是Windows系统保留名称",
                name_without_ext
            )));
        }
    }

    // Check for names ending with space or period (problematic on Windows)
    if trimmed.ends_with(' ') || trimmed.ends_with('.') {
        return Err(Error::ValidationError(
            "文件名不能以空格或句点结尾".to_string()
        ));
    }

    // Check extension if specified
    if let Some(expected_ext) = expected_extension {
        let file_path = std::path::Path::new(trimmed);
        let actual_ext = file_path
            .extension()
            .and_then(|e| e.to_str())
            .unwrap_or("");

        if actual_ext.to_lowercase() != expected_ext.to_lowercase() {
            return Err(Error::ValidationError(format!(
                "文件扩展名不正确，期望: .{}",
                expected_ext
            )));
        }
    }

    Ok(trimmed.to_string())
}



/// Validates window settings
pub fn validate_window_settings(settings: &crate::models::WindowSettings) -> Result<()> {
    use crate::utils::constants::*;
    
    if settings.width < MIN_WINDOW_WIDTH || settings.width > MAX_WINDOW_WIDTH {
        return Err(Error::ValidationError(format!(
            "窗口宽度必须在 {} 到 {} 之间",
            MIN_WINDOW_WIDTH, MAX_WINDOW_WIDTH
        )));
    }
    
    if settings.height < MIN_WINDOW_HEIGHT || settings.height > MAX_WINDOW_HEIGHT {
        return Err(Error::ValidationError(format!(
            "窗口高度必须在 {} 到 {} 之间",
            MIN_WINDOW_HEIGHT, MAX_WINDOW_HEIGHT
        )));
    }
    
    Ok(())
}
