// RealityTap 触觉反馈错误处理工具
// 统一处理触觉反馈相关的错误，提供用户友好的错误信息

import type { HapticApiError } from '@/types/haptic-types';
import { LogModule, logger } from '@/utils/logger/logger';

/**
 * 触觉反馈错误类型枚举
 */
export enum HapticErrorType {
  // 初始化相关错误
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED',
  LIBRARY_NOT_LOADED = 'LIBRARY_NOT_LOADED',
  INVALID_CONFIG = 'INVALID_CONFIG',
  
  // 设备相关错误
  DEVICE_NOT_FOUND = 'DEVICE_NOT_FOUND',
  DEVICE_CONNECTION_FAILED = 'DEVICE_CONNECTION_FAILED',
  DEVICE_NOT_INITIALIZED = 'DEVICE_NOT_INITIALIZED',
  
  // 播放相关错误
  PLAYBACK_FAILED = 'PLAYBACK_FAILED',
  INVALID_HAPTIC_DATA = 'INVALID_HAPTIC_DATA',
  PLAYBACK_NOT_STARTED = 'PLAYBACK_NOT_STARTED',
  
  // 配置文件相关错误
  CONFIG_FILE_NOT_FOUND = 'CONFIG_FILE_NOT_FOUND',
  CONFIG_FILE_INVALID = 'CONFIG_FILE_INVALID',
  CONFIG_FILE_READ_ERROR = 'CONFIG_FILE_READ_ERROR',
  
  // 参数相关错误
  INVALID_AMPLITUDE = 'INVALID_AMPLITUDE',
  INVALID_EFFECT_ID = 'INVALID_EFFECT_ID',
  INVALID_DEVICE_ID = 'INVALID_DEVICE_ID',
  
  // 系统相关错误
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  MEMORY_ERROR = 'MEMORY_ERROR',
  THREAD_SYNC_ERROR = 'THREAD_SYNC_ERROR',
  
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  API_ERROR = 'API_ERROR',
}

/**
 * 触觉反馈错误类
 */
export class HapticError extends Error {
  public readonly type: HapticErrorType;
  public readonly code: string;
  public readonly details?: string;
  public readonly originalError?: any;
  public readonly timestamp: string;

  constructor(
    type: HapticErrorType,
    message: string,
    details?: string,
    originalError?: any
  ) {
    super(message);
    this.name = 'HapticError';
    this.type = type;
    this.code = type;
    this.details = details;
    this.originalError = originalError;
    this.timestamp = new Date().toISOString();
  }

  /**
   * 获取用户友好的错误信息
   */
  getUserFriendlyMessage(): string {
    return getErrorMessage(this.type, this.details);
  }

  /**
   * 获取完整的错误信息（用于日志）
   */
  getFullErrorInfo(): string {
    return `[${this.type}] ${this.message}${this.details ? ` - ${this.details}` : ''}`;
  }

  /**
   * 转换为 JSON 格式
   */
  toJSON() {
    return {
      name: this.name,
      type: this.type,
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
      stack: this.stack,
    };
  }
}

/**
 * 错误信息映射表（支持国际化）
 */
const ERROR_MESSAGES: Record<HapticErrorType, string> = {
  // 初始化相关错误
  [HapticErrorType.INITIALIZATION_FAILED]: '触觉反馈库初始化失败',
  [HapticErrorType.LIBRARY_NOT_LOADED]: '触觉反馈库未加载',
  [HapticErrorType.INVALID_CONFIG]: '设备配置无效',
  
  // 设备相关错误
  [HapticErrorType.DEVICE_NOT_FOUND]: '未找到指定的触觉设备',
  [HapticErrorType.DEVICE_CONNECTION_FAILED]: '触觉设备连接失败',
  [HapticErrorType.DEVICE_NOT_INITIALIZED]: '触觉设备未初始化',
  
  // 播放相关错误
  [HapticErrorType.PLAYBACK_FAILED]: '触觉效果播放失败',
  [HapticErrorType.INVALID_HAPTIC_DATA]: '触觉数据格式无效',
  [HapticErrorType.PLAYBACK_NOT_STARTED]: '播放系统未启动',
  
  // 配置文件相关错误
  [HapticErrorType.CONFIG_FILE_NOT_FOUND]: '配置文件不存在',
  [HapticErrorType.CONFIG_FILE_INVALID]: '配置文件格式无效',
  [HapticErrorType.CONFIG_FILE_READ_ERROR]: '配置文件读取失败',
  
  // 参数相关错误
  [HapticErrorType.INVALID_AMPLITUDE]: '振幅值无效，应在 0-255 范围内',
  [HapticErrorType.INVALID_EFFECT_ID]: '效果ID无效',
  [HapticErrorType.INVALID_DEVICE_ID]: '设备ID无效',
  
  // 系统相关错误
  [HapticErrorType.TIMEOUT_ERROR]: '操作超时',
  [HapticErrorType.MEMORY_ERROR]: '内存分配失败',
  [HapticErrorType.THREAD_SYNC_ERROR]: '线程同步错误',
  
  // 通用错误
  [HapticErrorType.UNKNOWN_ERROR]: '未知错误',
  [HapticErrorType.API_ERROR]: 'API调用失败',
};

/**
 * 获取错误信息
 * @param errorType 错误类型
 * @param details 详细信息
 * @returns 用户友好的错误信息
 */
export function getErrorMessage(errorType: HapticErrorType, details?: string): string {
  const baseMessage = ERROR_MESSAGES[errorType] || ERROR_MESSAGES[HapticErrorType.UNKNOWN_ERROR];
  return details ? `${baseMessage}: ${details}` : baseMessage;
}

/**
 * 从 Rust 后端错误信息解析错误类型
 * @param errorMessage 错误信息
 * @returns 错误类型
 */
export function parseErrorType(errorMessage: string): HapticErrorType {
  const message = errorMessage.toLowerCase();
  
  // 初始化相关
  if (message.includes('初始化') || message.includes('init')) {
    return HapticErrorType.INITIALIZATION_FAILED;
  }
  
  // 设备相关
  if (message.includes('设备') || message.includes('device')) {
    if (message.includes('未找到') || message.includes('not found')) {
      return HapticErrorType.DEVICE_NOT_FOUND;
    }
    if (message.includes('连接') || message.includes('connection')) {
      return HapticErrorType.DEVICE_CONNECTION_FAILED;
    }
    return HapticErrorType.DEVICE_NOT_INITIALIZED;
  }
  
  // 配置文件相关
  if (message.includes('配置文件') || message.includes('config')) {
    if (message.includes('不存在') || message.includes('not exist')) {
      return HapticErrorType.CONFIG_FILE_NOT_FOUND;
    }
    if (message.includes('无效') || message.includes('invalid')) {
      return HapticErrorType.CONFIG_FILE_INVALID;
    }
    return HapticErrorType.CONFIG_FILE_READ_ERROR;
  }
  
  // 播放相关
  if (message.includes('播放') || message.includes('play')) {
    if (message.includes('未启动') || message.includes('not started')) {
      return HapticErrorType.PLAYBACK_NOT_STARTED;
    }
    return HapticErrorType.PLAYBACK_FAILED;
  }
  
  // 参数相关
  if (message.includes('振幅') || message.includes('amplitude')) {
    return HapticErrorType.INVALID_AMPLITUDE;
  }
  
  // 超时相关
  if (message.includes('超时') || message.includes('timeout')) {
    return HapticErrorType.TIMEOUT_ERROR;
  }
  
  return HapticErrorType.UNKNOWN_ERROR;
}

/**
 * 创建触觉反馈错误
 * @param type 错误类型
 * @param details 详细信息
 * @param originalError 原始错误
 * @returns HapticError 实例
 */
export function createHapticError(
  type: HapticErrorType,
  details?: string,
  originalError?: any
): HapticError {
  const message = getErrorMessage(type, details);
  return new HapticError(type, message, details, originalError);
}

/**
 * 从 API 错误创建触觉反馈错误
 * @param apiError API 错误
 * @returns HapticError 实例
 */
export function fromApiError(apiError: HapticApiError): HapticError {
  const errorType = parseErrorType(apiError.message);
  return new HapticError(
    errorType,
    apiError.message,
    apiError.details,
    apiError.originalError
  );
}

/**
 * 错误处理器类
 */
export class HapticErrorHandler {
  private static errorHistory: HapticError[] = [];
  private static maxHistorySize = 100;

  /**
   * 处理错误
   * @param error 错误对象
   * @param context 错误上下文
   * @returns 处理后的 HapticError
   */
  static handle(error: any, context?: string): HapticError {
    let hapticError: HapticError;

    if (error instanceof HapticError) {
      hapticError = error;
    } else if (error && typeof error === 'object' && 'code' in error) {
      // API 错误
      hapticError = fromApiError(error as HapticApiError);
    } else {
      // 通用错误
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorType = parseErrorType(errorMessage);
      hapticError = createHapticError(errorType, context, error);
    }

    // 记录错误日志
    logger.error(LogModule.HAPTIC, hapticError.getFullErrorInfo(), {
      error: hapticError.toJSON(),
      context,
    });

    // 添加到错误历史
    this.addToHistory(hapticError);

    return hapticError;
  }

  /**
   * 添加错误到历史记录
   * @param error 错误对象
   */
  private static addToHistory(error: HapticError): void {
    this.errorHistory.unshift(error);
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * 获取错误历史记录
   * @param limit 限制数量
   * @returns 错误历史数组
   */
  static getErrorHistory(limit?: number): HapticError[] {
    return limit ? this.errorHistory.slice(0, limit) : [...this.errorHistory];
  }

  /**
   * 清空错误历史记录
   */
  static clearErrorHistory(): void {
    this.errorHistory = [];
  }

  /**
   * 获取最近的错误
   * @returns 最近的错误或 null
   */
  static getLastError(): HapticError | null {
    return this.errorHistory[0] || null;
  }

  /**
   * 检查是否有特定类型的错误
   * @param errorType 错误类型
   * @returns 是否存在该类型错误
   */
  static hasErrorType(errorType: HapticErrorType): boolean {
    return this.errorHistory.some(error => error.type === errorType);
  }
}

/**
 * 错误处理装饰器工厂
 * @param context 错误上下文
 * @returns 装饰器函数
 */
export function handleHapticError(context?: string) {
  return function <T extends any[], R>(
    _target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<(...args: T) => Promise<R>>
  ) {
    const originalMethod = descriptor.value!;
    
    descriptor.value = async function (...args: T): Promise<R> {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        const hapticError = HapticErrorHandler.handle(error, context || propertyKey);
        throw hapticError;
      }
    };
    
    return descriptor;
  };
}
