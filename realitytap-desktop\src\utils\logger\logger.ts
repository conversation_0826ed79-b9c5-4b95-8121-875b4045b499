/**
 * RealityTap Studio 统一日志记录器
 * 
 * 使用方法：
 * ```typescript
 * import { logger } from '@/utils/logger/logger';
 * 
 * // 基础使用
 * logger.info(LogModule.WAVEFORM, '波形数据已加载');
 * logger.warn(LogModule.DEVICE, '设备连接不稳定', { deviceId: 'xxx' });
 * logger.error(LogModule.AUDIO, '音频处理失败', error);
 * 
 * // 性能敏感区域使用
 * logger.debug(LogModule.DRAG, '拖拽位置更新', { x, y });
 * logger.performance(LogModule.WAVEFORM, '绘制完成', drawTime);
 * ```
 */

import {
  LogLevel,
  LogModule,
  shouldLog,
  formatLogMessage,
  logBuffer,
  LOGGER_CONFIG,
  HIGH_FREQUENCY_MODULES
} from './loggerConfig';

/**
 * 节流控制器
 * 用于控制高频日志的输出频率
 */
class ThrottleController {
  private lastLogTime = new Map<string, number>();
  private throttleInterval = 100; // 100ms 节流间隔
  
  shouldThrottle(key: string): boolean {
    const now = Date.now();
    const lastTime = this.lastLogTime.get(key) || 0;
    
    if (now - lastTime < this.throttleInterval) {
      return true;
    }
    
    this.lastLogTime.set(key, now);
    return false;
  }
  
  setThrottleInterval(interval: number): void {
    this.throttleInterval = interval;
  }
}

const throttleController = new ThrottleController();

/**
 * 统一日志记录器类
 */
class Logger {
  private isEnabled = true;
  
  /**
   * 启用/禁用日志记录
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
  
  /**
   * 检查是否应该记录日志（包含节流控制）
   */
  private shouldLogWithThrottle(module: LogModule, level: LogLevel, message: string): boolean {
    if (!this.isEnabled) return false;
    if (!shouldLog(module, level)) return false;
    
    // 对高频模块进行节流控制
    if (HIGH_FREQUENCY_MODULES.includes(module) && level === LogLevel.DEBUG) {
      const throttleKey = `${module}-${message.slice(0, 20)}`;
      if (throttleController.shouldThrottle(throttleKey)) {
        return false;
      }
    }
    
    return true;
  }
  
  /**
   * 通用日志记录方法
   */
  private log(
    level: LogLevel,
    module: LogModule,
    message: string,
    data?: any,
    consoleMethod: 'log' | 'info' | 'warn' | 'error' | 'debug' = 'log'
  ): void {
    if (!this.shouldLogWithThrottle(module, level, message)) {
      return;
    }
    
    const formattedMessage = formatLogMessage(module, level, message, data);
    
    // 添加到缓冲区
    logBuffer.add(formattedMessage);
    
    // 输出到控制台
    if (data !== undefined && !LOGGER_CONFIG.productionMode) {
      console[consoleMethod](formattedMessage, data);
    } else {
      console[consoleMethod](formattedMessage);
    }
  }
  
  /**
   * 调试级别日志
   */
  debug(module: LogModule, message: string, data?: any): void {
    this.log(LogLevel.DEBUG, module, message, data, 'debug');
  }
  
  /**
   * 信息级别日志
   */
  info(module: LogModule, message: string, data?: any): void {
    this.log(LogLevel.INFO, module, message, data, 'info');
  }
  
  /**
   * 警告级别日志
   */
  warn(module: LogModule, message: string, data?: any): void {
    this.log(LogLevel.WARN, module, message, data, 'warn');
  }
  
  /**
   * 错误级别日志
   */
  error(module: LogModule, message: string, data?: any): void {
    this.log(LogLevel.ERROR, module, message, data, 'error');
  }
  
  /**
   * 性能监控专用日志
   */
  performance(module: LogModule, operation: string, duration: number, data?: any): void {
    if (!LOGGER_CONFIG.enablePerformanceLogging) return;
    
    const message = `${operation} 耗时: ${duration.toFixed(2)}ms`;
    this.debug(module, message, data);
  }
  
  /**
   * 拖拽操作专用日志
   */
  drag(message: string, data?: any): void {
    if (!LOGGER_CONFIG.enableDragLogging) return;
    
    this.debug(LogModule.DRAG, message, data);
  }
  
  /**
   * 条件日志记录
   */
  conditional(
    condition: boolean,
    level: LogLevel,
    module: LogModule,
    message: string,
    data?: any
  ): void {
    if (condition) {
      this.log(level, module, message, data);
    }
  }
  
  /**
   * 分组日志开始
   */
  group(module: LogModule, title: string): void {
    if (shouldLog(module, LogLevel.DEBUG)) {
      console.group(formatLogMessage(module, LogLevel.DEBUG, title));
    }
  }
  
  /**
   * 分组日志结束
   */
  groupEnd(): void {
    console.groupEnd();
  }
  
  /**
   * 获取日志缓冲区内容
   */
  getLogBuffer(): string[] {
    return logBuffer.getAll();
  }
  
  /**
   * 清空日志缓冲区
   */
  clearLogBuffer(): void {
    logBuffer.clear();
  }
  
  /**
   * 导出日志到文件
   */
  exportLogs(): string {
    return logBuffer.export();
  }
  
  /**
   * 设置节流间隔
   */
  setThrottleInterval(interval: number): void {
    throttleController.setThrottleInterval(interval);
  }
}

// 导出单例实例
export const logger = new Logger();

// 重新导出配置中的枚举和类型
export { LogLevel, LogModule } from './loggerConfig';

// 导出便捷方法
export const log = {
  debug: (module: LogModule, message: string, data?: any) => logger.debug(module, message, data),
  info: (module: LogModule, message: string, data?: any) => logger.info(module, message, data),
  warn: (module: LogModule, message: string, data?: any) => logger.warn(module, message, data),
  error: (module: LogModule, message: string, data?: any) => logger.error(module, message, data),
  performance: (module: LogModule, operation: string, duration: number, data?: any) =>
    logger.performance(module, operation, duration, data),
  drag: (message: string, data?: any) => logger.drag(message, data)
};

// 向后兼容的全局方法
export const enableLogging = (enabled: boolean) => logger.setEnabled(enabled);

/**
 * 创建模块专用日志记录器
 */
export const createModuleLogger = (module: LogModule) => ({
  debug: (message: string, data?: any) => logger.debug(module, message, data),
  info: (message: string, data?: any) => logger.info(module, message, data),
  warn: (message: string, data?: any) => logger.warn(module, message, data),
  error: (message: string, data?: any) => logger.error(module, message, data),
  performance: (operation: string, duration: number, data?: any) => 
    logger.performance(module, operation, duration, data)
});

// 预定义的模块日志记录器
export const waveformLogger = createModuleLogger(LogModule.WAVEFORM);
export const deviceLogger = createModuleLogger(LogModule.DEVICE);
export const audioLogger = createModuleLogger(LogModule.AUDIO);
export const projectLogger = createModuleLogger(LogModule.PROJECT);
export const performanceLogger = createModuleLogger(LogModule.PERFORMANCE);
export const dragLogger = createModuleLogger(LogModule.DRAG);
export const hapticLogger = createModuleLogger(LogModule.HAPTIC);

// 默认导出
export default logger;
