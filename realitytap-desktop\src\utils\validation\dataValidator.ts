/**
 * RealityTap 数据验证工具
 * 验证输入数据的完整性和范围正确性
 */

import type { ConvertToArrayInput, ValidationResult, ConvertToArrayOptions, ConvertToArrayError, ConvertToArrayErrorType } from "@/types/reality-tap-converter";
import { VALIDATION_RANGES } from "@/types/reality-tap-converter";
import type { RenderableEvent } from "@/types/haptic-editor";
import { logger, LogModule } from "@/utils/logger/logger";

/**
 * 验证 RealityTap 输入数据
 * @param input 输入数据
 * @param version 目标版本
 * @param options 验证选项
 * @returns 验证结果
 */
export function validateRealityTapData(input: ConvertToArrayInput, version: 1 | 2, options: ConvertToArrayOptions = {}): ValidationResult {
  const startTime = performance.now();
  let totalFields = 0;
  let validFields = 0;

  try {
    if (options.enableLogging) {
      logger.debug(LogModule.WAVEFORM, "[DataValidator] 开始数据验证", {
        version,
        strictMode: options.strictMode,
        inputType: typeof input,
        isArray: Array.isArray(input),
        inputSize: Array.isArray(input) ? input.length : Object.keys(input || {}).length
      });
    }

    // 1. 基础输入验证
    const basicValidation = validateBasicInput(input);
    if (!basicValidation.isValid) {
      if (options.enableLogging) {
        logger.error(LogModule.WAVEFORM, "[DataValidator] 基础输入验证失败", {
          errors: basicValidation.errors,
          validationTime: `${(performance.now() - startTime).toFixed(2)}ms`
        });
      }
      return basicValidation;
    }

    // 2. 根据输入类型进行具体验证
    let validationResult: ValidationResult;

    if (Array.isArray(input)) {
      if (options.enableLogging) {
        logger.debug(LogModule.WAVEFORM, "[DataValidator] 验证 RenderableEvent 数组", {
          eventCount: input.length
        });
      }
      validationResult = validateRenderableEvents(input as RenderableEvent[], options);
    } else {
      if (options.enableLogging) {
        logger.debug(LogModule.WAVEFORM, "[DataValidator] 验证 RealityTapEffect 对象", {
          version,
          hasMetadata: !!(input as any)?.Metadata,
          hasPattern: !!(input as any)?.Pattern,
          hasPatternList: !!(input as any)?.PatternList
        });
      }
      validationResult = validateRealityTapEffect(input, version, options);
    }

    const validationTime = performance.now() - startTime;

    if (options.enableLogging) {
      const logLevel = validationResult.isValid ? 'info' : 'warn';
      const logMessage = validationResult.isValid ?
        "[DataValidator] 数据验证完成" :
        "[DataValidator] 数据验证完成，存在问题";

      logger[logLevel](LogModule.WAVEFORM, logMessage, {
        isValid: validationResult.isValid,
        errorCount: validationResult.errors.length,
        warningCount: validationResult.warnings.length,
        validationTime: `${validationTime.toFixed(2)}ms`,
        version
      });
    }

    return validationResult;

  } catch (error) {
    const errorMessage = `数据验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`;
    const validationTime = performance.now() - startTime;

    if (options.enableLogging) {
      logger.error(LogModule.WAVEFORM, "[DataValidator] 验证过程异常", {
        error: error instanceof Error ? error.message : String(error),
        errorType: error instanceof Error ? error.constructor.name : typeof error,
        validationTime: `${validationTime.toFixed(2)}ms`,
        version,
        inputType: typeof input,
        isArray: Array.isArray(input)
      });
    }

    return {
      isValid: false,
      errors: [errorMessage],
      warnings: [],
      fieldStats: { totalFields, validFields, invalidFields: totalFields - validFields },
    };
  }
}

/**
 * 验证基础输入
 */
function validateBasicInput(input: ConvertToArrayInput): ValidationResult {
  const errors: string[] = [];

  if (input === null || input === undefined) {
    errors.push("输入数据不能为空");
  } else if (typeof input !== "object") {
    errors.push("输入数据必须是对象或数组类型");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
  };
}

/**
 * 验证 RenderableEvent 数组
 */
function validateRenderableEvents(events: RenderableEvent[], options: ConvertToArrayOptions): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let totalFields = 0;
  let validFields = 0;

  if (events.length === 0) {
    warnings.push("事件数组为空");
  }

  for (let i = 0; i < events.length; i++) {
    const event = events[i];
    const eventValidation = validateRenderableEvent(event, i, options);

    errors.push(...eventValidation.errors);
    warnings.push(...eventValidation.warnings);

    if (eventValidation.fieldStats) {
      totalFields += eventValidation.fieldStats.totalFields;
      validFields += eventValidation.fieldStats.validFields;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fieldStats: { totalFields, validFields, invalidFields: totalFields - validFields },
  };
}

/**
 * 验证单个 RenderableEvent
 */
function validateRenderableEvent(event: RenderableEvent, index: number, options: ConvertToArrayOptions): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let totalFields = 4; // 基础字段：type, id, startTime, intensity/frequency
  let validFields = 0;

  const eventPrefix = `事件[${index}]`;

  // 验证基础字段
  if (!event.type || !["transient", "continuous"].includes(event.type)) {
    errors.push(`${eventPrefix}: 无效的事件类型 "${event.type}"`);
  } else {
    validFields++;
  }

  if (!event.id || typeof event.id !== "string") {
    errors.push(`${eventPrefix}: 缺少有效的事件ID`);
  } else {
    validFields++;
  }

  if (typeof event.startTime !== "number" || event.startTime < 0) {
    errors.push(`${eventPrefix}: 无效的开始时间 "${event.startTime}"`);
  } else {
    validFields++;
  }

  // 根据事件类型验证特定字段
  if (event.type === "transient") {
    const transientValidation = validateTransientEvent(event, eventPrefix, options);
    errors.push(...transientValidation.errors);
    warnings.push(...transientValidation.warnings);

    if (transientValidation.fieldStats) {
      totalFields += transientValidation.fieldStats.totalFields;
      validFields += transientValidation.fieldStats.validFields;
    }
  } else if (event.type === "continuous") {
    const continuousValidation = validateContinuousEvent(event, eventPrefix, options);
    errors.push(...continuousValidation.errors);
    warnings.push(...continuousValidation.warnings);

    if (continuousValidation.fieldStats) {
      totalFields += continuousValidation.fieldStats.totalFields;
      validFields += continuousValidation.fieldStats.validFields;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fieldStats: { totalFields, validFields, invalidFields: totalFields - validFields },
  };
}

/**
 * 验证瞬时事件
 */
function validateTransientEvent(event: any, eventPrefix: string, options: ConvertToArrayOptions): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let totalFields = 4; // intensity, frequency, width, peakTime/stopTime
  let validFields = 0;

  // 验证强度
  if (!isValidRange(event.intensity, VALIDATION_RANGES.INTENSITY)) {
    errors.push(`${eventPrefix}: 强度值 "${event.intensity}" 超出范围 [${VALIDATION_RANGES.INTENSITY.min}-${VALIDATION_RANGES.INTENSITY.max}]`);
  } else {
    validFields++;
  }

  // 验证频率
  if (!isValidRange(event.frequency, VALIDATION_RANGES.FREQUENCY)) {
    errors.push(`${eventPrefix}: 频率值 "${event.frequency}" 超出范围 [${VALIDATION_RANGES.FREQUENCY.min}-${VALIDATION_RANGES.FREQUENCY.max}]`);
  } else {
    validFields++;
  }

  // 验证宽度
  if (typeof event.width !== "number" || event.width <= 0) {
    errors.push(`${eventPrefix}: 无效的宽度值 "${event.width}"`);
  } else {
    validFields++;
  }

  // 验证时间一致性
  if (typeof event.peakTime === "number" && typeof event.stopTime === "number") {
    if (event.peakTime > event.stopTime) {
      errors.push(`${eventPrefix}: 峰值时间不能大于结束时间`);
    } else {
      validFields++;
    }
  } else {
    if (options.strictMode) {
      errors.push(`${eventPrefix}: 缺少峰值时间或结束时间`);
    } else {
      warnings.push(`${eventPrefix}: 缺少峰值时间或结束时间`);
      validFields++; // 非严格模式下不算错误
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fieldStats: { totalFields, validFields, invalidFields: totalFields - validFields },
  };
}

/**
 * 验证连续事件
 */
function validateContinuousEvent(event: any, eventPrefix: string, options: ConvertToArrayOptions): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let totalFields = 5; // eventIntensity, eventFrequency, duration, stopTime, curves
  let validFields = 0;

  // 验证事件强度
  if (!isValidRange(event.eventIntensity, VALIDATION_RANGES.INTENSITY)) {
    errors.push(`${eventPrefix}: 事件强度值 "${event.eventIntensity}" 超出范围`);
  } else {
    validFields++;
  }

  // 验证事件频率
  if (!isValidRange(event.eventFrequency, VALIDATION_RANGES.FREQUENCY)) {
    errors.push(`${eventPrefix}: 事件频率值 "${event.eventFrequency}" 超出范围`);
  } else {
    validFields++;
  }

  // 验证持续时间
  if (typeof event.duration !== "number" || event.duration <= 0) {
    errors.push(`${eventPrefix}: 无效的持续时间 "${event.duration}"`);
  } else {
    validFields++;
  }

  // 验证时间一致性
  if (typeof event.stopTime === "number") {
    if (event.stopTime <= event.startTime) {
      errors.push(`${eventPrefix}: 结束时间必须大于开始时间`);
    } else {
      validFields++;
    }
  } else {
    if (options.strictMode) {
      errors.push(`${eventPrefix}: 缺少结束时间`);
    } else {
      warnings.push(`${eventPrefix}: 缺少结束时间`);
      validFields++;
    }
  }

  // 验证曲线数据
  if (!Array.isArray(event.curves)) {
    errors.push(`${eventPrefix}: 缺少曲线数据`);
  } else {
    const curveValidation = validateCurvePoints(event.curves, eventPrefix, options);
    errors.push(...curveValidation.errors);
    warnings.push(...curveValidation.warnings);

    if (curveValidation.fieldStats) {
      totalFields += curveValidation.fieldStats.totalFields;
      validFields += curveValidation.fieldStats.validFields;
    }

    if (curveValidation.isValid) {
      validFields++;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fieldStats: { totalFields, validFields, invalidFields: totalFields - validFields },
  };
}

/**
 * 验证曲线点数据
 */
function validateCurvePoints(curves: any[], eventPrefix: string, options: ConvertToArrayOptions): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let totalFields = curves.length * 4; // 每个曲线点4个字段
  let validFields = 0;

  if (curves.length === 0) {
    errors.push(`${eventPrefix}: 曲线数据不能为空`);
    return { isValid: false, errors, warnings };
  }

  for (let i = 0; i < curves.length; i++) {
    const curve = curves[i];
    const curvePrefix = `${eventPrefix}.曲线[${i}]`;

    // 验证时间偏移
    if (typeof curve.timeOffset !== "number" || curve.timeOffset < 0) {
      errors.push(`${curvePrefix}: 无效的时间偏移 "${curve.timeOffset}"`);
    } else {
      validFields++;
    }

    // 验证原始强度
    if (!isValidRange(curve.rawIntensity, VALIDATION_RANGES.CURVE_INTENSITY)) {
      errors.push(`${curvePrefix}: 原始强度值 "${curve.rawIntensity}" 超出范围 [0-1]`);
    } else {
      validFields++;
    }

    // 验证相对频率
    if (!isValidRange(curve.relativeCurveFrequency, VALIDATION_RANGES.RELATIVE_FREQUENCY)) {
      errors.push(`${curvePrefix}: 相对频率值 "${curve.relativeCurveFrequency}" 超出范围`);
    } else {
      validFields++;
    }

    // 验证计算后的频率
    if (typeof curve.curveFrequency === "number") {
      validFields++;
    } else {
      if (options.strictMode) {
        errors.push(`${curvePrefix}: 缺少计算后的频率值`);
      } else {
        warnings.push(`${curvePrefix}: 缺少计算后的频率值`);
        validFields++;
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fieldStats: { totalFields, validFields, invalidFields: totalFields - validFields },
  };
}

/**
 * 验证 RealityTapEffect 对象
 */
function validateRealityTapEffect(input: any, version: 1 | 2, _options: ConvertToArrayOptions): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证元数据
  if (!input.Metadata && !input.metadata) {
    errors.push("缺少元数据信息");
  } else {
    const metadata = input.Metadata || input.metadata;
    if (metadata.Version !== version && metadata.version !== version) {
      warnings.push(`元数据版本 (${metadata.Version || metadata.version}) 与目标版本 (${version}) 不匹配`);
    }
  }

  // 根据版本验证数据结构
  if (version === 1) {
    if (!input.Pattern && !input.pattern) {
      errors.push("V1 格式缺少 Pattern 字段");
    }
  } else if (version === 2) {
    if (!input.PatternList && !input.patternList) {
      errors.push("V2 格式缺少 PatternList 字段");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fieldStats: { totalFields: 2, validFields: errors.length === 0 ? 2 : 0, invalidFields: errors.length },
  };
}

/**
 * 检查数值是否在有效范围内
 */
function isValidRange(value: any, range: { min: number; max: number }): boolean {
  return typeof value === "number" && !isNaN(value) && value >= range.min && value <= range.max;
}

/**
 * 创建验证错误对象
 */
export function createValidationError(type: ConvertToArrayErrorType, message: string, details?: any, eventId?: string, eventIndex?: number): ConvertToArrayError {
  return {
    type,
    message,
    details,
    eventId,
    eventIndex,
  };
}
