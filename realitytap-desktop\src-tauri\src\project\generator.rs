// Project generation and refresh utilities
use crate::{
    error::{Error, Result},
    models,
    project::io::{HAPTICS_DIR_NAME, AUDIO_DIR_NAME, write_project_directory, read_project_directory},
    utils::{constants::AUDIO_FORMATS_FOR_SEARCH, directory_walker},
};
use chrono::Utc;
use std::collections::HashMap;
use std::path::Path;
use uuid::Uuid;

/// 根据任意目录自动生成 RealityTap 项目结构（groups、files、音频关联等）
pub fn generate_project_from_directory(project_dir_path: &Path) -> Result<models::Project> {
    // 1. 校验 audio、haptics 目录
    let haptics_dir = project_dir_path.join(HAPTICS_DIR_NAME);
    let audio_dir = project_dir_path.join(AUDIO_DIR_NAME);
    if !haptics_dir.is_dir() || !audio_dir.is_dir() {
        return Err(Error::ValidationError(format!(
            "项目目录必须包含 audio 和 haptics 子目录: {:?}",
            project_dir_path
        )));
    }

    // 2. 递归扫描 haptics 目录，生成 groups
    let mut groups = Vec::new();
    let mut group_path_map: HashMap<String, Uuid> = HashMap::new();
    let mut group_parent_map: HashMap<String, Option<Uuid>> = HashMap::new();

    // 根分组 parent_group_uuid = None，rel_path = ""
    if haptics_dir.is_dir() {
        for entry in std::fs::read_dir(&haptics_dir)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_dir() {
                let rel_path = entry.file_name().to_string_lossy().to_string();
                directory_walker::walk_groups(
                    &path,
                    &rel_path,
                    None,
                    &mut groups,
                    &mut group_path_map,
                    &mut group_parent_map,
                );
            }
        }
    }

    // 3. 递归扫描 haptics 目录，生成 files
    let mut files = Vec::new();
    let now = Utc::now();
    let audio_formats = ["wav", "mp3", "ogg"];

    directory_walker::walk_files(
        &haptics_dir,
        "",
        &group_path_map,
        &mut files,
        &audio_dir,
        &audio_formats,
        now,
        None, // No existing files map for generate operation
    )?;

    // 4. 组装 RealityTapProject 结构体
    let project_name = project_dir_path
        .file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("")
        .to_string();
    let project = models::Project {
        project_uuid: Uuid::new_v4(),
        project_name,
        create_time: now,
        last_modified_time: now,
        author: "RealityTap Studio".to_string(),
        version: "1.0.0".to_string(),
        description: String::new(),
        tags: vec![],
        groups,
        files,
    };
    Ok(project)
}

/// 刷新已有项目，重新遍历所有子目录和文件，更新 groups、files
pub fn refresh_project_directory(project_dir_path: &Path) -> Result<models::Project> {
    // 1. 读取原 project.json
    let project = read_project_directory(project_dir_path)?;

    // 2. 校验 audio、haptics 目录
    let haptics_dir = project_dir_path.join(HAPTICS_DIR_NAME);
    let audio_dir = project_dir_path.join(AUDIO_DIR_NAME);
    if !haptics_dir.is_dir() || !audio_dir.is_dir() {
        return Err(Error::ValidationError(format!(
            "项目目录必须包含 audio 和 haptics 子目录: {:?}",
            project_dir_path
        )));
    }

    // 3. 递归扫描 haptics 目录，生成 groups
    let mut groups = Vec::new();
    let mut group_path_map: HashMap<String, Uuid> = HashMap::new();
    let mut group_parent_map: HashMap<String, Option<Uuid>> = HashMap::new();

    // 创建现有分组的映射，用于保留UUID
    let existing_groups_map: HashMap<String, &models::Group> =
        project.groups.iter().map(|g| (g.path.clone(), g)).collect();

    // 根分组 parent_group_uuid = None，rel_path = ""
    if haptics_dir.is_dir() {
        for entry in std::fs::read_dir(&haptics_dir)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_dir() {
                let rel_path = entry.file_name().to_string_lossy().to_string();
                directory_walker::walk_groups_with_existing(
                    &path,
                    &rel_path,
                    None,
                    &mut groups,
                    &mut group_path_map,
                    &mut group_parent_map,
                    Some(&existing_groups_map),
                );
            }
        }
    }

    // 4. 递归扫描 haptics 目录，生成 files
    let mut files = Vec::new();
    let now = Utc::now();

    // 创建现有文件的映射，用于保留元数据
    let existing_files_map: HashMap<String, &models::HapticFile> =
        project.files.iter().map(|f| (f.path.clone(), f)).collect();

    directory_walker::walk_files(
        &haptics_dir,
        "",
        &group_path_map,
        &mut files,
        &audio_dir,
        AUDIO_FORMATS_FOR_SEARCH,
        now,
        Some(&existing_files_map), // Pass existing files map for refresh operation
    )?;

    // 5. 检查是否有实际变化
    let has_changes = groups != project.groups || files != project.files;

    let refreshed_project = if has_changes {
        // 有变化时，组装新的项目结构体，更新 lastModifiedTime
        let updated_project = models::Project {
            project_uuid: project.project_uuid,
            project_name: project.project_name,
            create_time: project.create_time,
            last_modified_time: now,
            author: project.author,
            version: project.version,
            description: project.description,
            tags: project.tags,
            groups,
            files,
        };

        // 6. 写回 project.json
        write_project_directory(&updated_project, project_dir_path)?;
        log::info!("项目目录有变化，已更新 project.json: {:?}", project_dir_path);
        updated_project
    } else {
        // 无变化时，返回原项目数据，不修改文件
        log::debug!("项目目录无变化，跳过写入: {:?}", project_dir_path);
        project
    };

    Ok(refreshed_project)
}
