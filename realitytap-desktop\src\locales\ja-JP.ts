/**
 * 日本語言語パック
 */

export default {
  // === common ===
  common: {
    confirm: "確認",
    cancel: "キャンセル",
    cancelled: "操作がキャンセルされました",
    save: "保存",
    delete: "削除",
    close: "閉じる",
    loading: "読み込み中...",
    error: "エラー",
    clear: "クリア",
    about: "について",
    retry: "再試行",
    dismiss: "無視",
    enabled: "有効",
    disabled: "無効"
  },
  // === app ===
  app: {
    title: "RealityTap Haptics Studio"
  },
  // === dashboard ===
  dashboard: {
    // === tabs ===
    tabs: {
      projects: "プロジェクト",
      learning: "学習リソース"
    },
    // === projects ===
    projects: {
      newProject: "新しいプロジェクト",
      newProjectSubtitle: "オーディオまたはハプティックファイルから",
      openProject: "プロジェクトを開く",
      openProjectSubtitle: "ローカルファイルから",
      recentProjects: "最近のプロジェクト",
      exampleProjects: "サンプルプロジェクト",
      noRecentProjects: "最近のプロジェクト記録がありません",
      goToLearning: "学習ページへ"
    }
  },
  // === project ===
  project: {
    // === create ===
    create: {
      success: "プロジェクトが正常に作成されました",
      failed: "プロジェクトの作成に失敗しました"
    },
    // === open ===
    open: {
      failed: "プロジェクトを開くのに失敗しました",
      notFound: "プロジェクトが見つかりません"
    },
    // === save ===
    save: {
      saving: "保存中...",
      success: "ファイルが正常に保存されました",
      noData: "保存するイベントデータがありません",
      noFileSelected: "ファイルが選択されていません",
      saveFile: "ファイルを保存 (Ctrl+S)",
      noChanges: "ファイルは変更されていないか、既に保存されています"
    },
    // === undo ===
    undo: {
      undoAction: "元に戻す (Ctrl+Z)",
      noUndoAvailable: "元に戻す操作がありません",
      noFileSelected: "ファイルが選択されていません"
    },
    // === redo ===
    redo: {
      redoAction: "やり直し (Ctrl+Y)",
      noRedoAvailable: "やり直し操作がありません",
      noFileSelected: "ファイルが選択されていません"
    }
  },
  // === editor ===
  editor: {
    // === navigation ===
    navigation: {
      projects: "プロジェクト",
      doubleClickToEdit: "ダブルクリックで編集",
      unsavedChanges: "未保存の変更があります"
    },
    // === eventProperties ===
    eventProperties: {
      title: "イベントプロパティ",
      transientEvent: "瞬時イベント",
      continuousEvent: "連続イベント",
      startTime: "開始時間",
      intensity: "強度",
      frequency: "周波数",
      duration: "持続時間",
      globalIntensity: "強度（グローバル）",
      globalFrequency: "周波数（グローバル）",
      selectedCurvePoint: "選択されたカーブポイント",
      pointNumber: "ポイント #",
      time: "時間",
      pointRelativeIntensity: "ポイント相対強度",
      pointRelativeFrequency: "ポイント相対周波数",
      computedAbsoluteIntensity: "計算された絶対強度",
      computedAbsoluteFrequency: "計算された絶対周波数",
      firstLastPointZeroIntensity: "最初と最後のポイントは常に強度ゼロ",
      frequencyAdjustmentHint: "ヒント: {key}キーを押しながら垂直にドラッグして周波数を調整",
      selectEventToAdjust: "プロパティを調整するには、タイムライン上のイベントを選択してください。"
    },
    // === duration ===
    duration: {
      increased: "持続時間が {duration} ミリ秒増加しました"
    },
    // === durationPanel ===
    durationPanel: {
      increaseDuration: "持続時間を増加",
      milliseconds: "ミリ秒",
      confirm: "確認"
    },
    // === empty ===
    empty: {
      title: "新しいハプティックファイルを追加",
      description: "単一のオーディオファイルまたはアセットフォルダをドラッグ&ドロップして、ハプティックの設計を開始してください",
      addHapticFile: "ハプティックファイルを追加",
      addAudioFile: "オーディオファイルを追加",
      addVideoFile: "ビデオファイルを追加"
    },
    // === waveform ===
    waveform: {
      noFileSelected: "編集するには、まずRealityTapハプティック波形ファイルを開いてください",
      loading: "RealityTapハプティック効果を読み込み中..."
    },
    // === project ===
    project: {
      untitled: "無題のプロジェクト",
      renameSuccess: "プロジェクト名が変更され、ディレクトリが名前変更されました",
      renameFailed: "プロジェクトの名前変更に失敗しました",
      noProjectLoaded: "現在プロジェクトが読み込まれていません。プロジェクトを作成または開いてください。"
    },
    // === file ===
    file: {
      confirmDelete: "削除の確認",
      confirmDeleteMessage: "ファイル \"{name}\" を削除してもよろしいですか？この操作は元に戻せません。",
      createSuccess: "ハプティックファイルが正常に作成されました",
      deleteSuccess: "ファイル \"{name}\" が正常に削除されました",
      deleteFailed: "ファイル \"{name}\" の削除に失敗しました：{error}",
      selectFirst: "まずファイルを選択してください",
      noFileSelected: "ファイルが選択されていません",
      noEventData: "保存するイベントデータがありません",
      saveSuccess: "ファイルが正常に保存されました"
    },
    // === audio ===
    audio: {
      importingToRoot: "ルートディレクトリにオーディオファイルをインポート中...",
      importingToGroup: "グループ \"{name}\" にオーディオファイルをインポート中...",
      importSuccess: "オーディオのインポートと.heファイルの生成が成功しました",
      importFailed: "オーディオのインポートに失敗しました：{error}",
      metadataFailed: "オーディオメタデータの解析に失敗しましたが、インポートは完了しました（持続時間を取得できませんでした）",
      amplitudeLoadFailed: "オーディオ振幅データの読み込みに失敗しました：{error}",
      processingFailed: "オーディオ処理に失敗しました：{error}",
      loadSuccess: "オーディオデータの読み込みが成功しました",
      loadFailed: "オーディオデータの読み込みに失敗しました",
      noAudioFile: "現在のファイルには関連するオーディオファイルがありません",
      checkFailed: "オーディオデータのチェックに失敗しました",
      waveformDisplay: "オーディオ波形表示",
      noAudioData: "オーディオデータがありません"
    },
    // === video ===
    video: {
      importingToRoot: "ルートディレクトリにビデオファイルをインポート中...",
      importingToGroup: "グループ \"{name}\" にビデオファイルをインポート中...",
      importSuccess: "ビデオのインポートと.heファイルの生成が成功しました",
      metadataFailed: "ビデオメタデータの解析に失敗しましたが、インポートは完了しました（持続時間を取得できませんでした）"
    },
    // === contextMenu ===
    contextMenu: {
      playEffect: "エフェクトを再生",
      renameFile: "ファイル名を変更",
      deleteFile: "ハプティックファイルを削除",
      renameGroup: "現在のグループ名を変更",
      deleteGroup: "現在のグループを削除",
      newHapticFile: "新しいハプティックファイル",
      importAudioFile: "オーディオファイルをインポート",
      importVideoFile: "ビデオファイルをインポート",
      newRootGroup: "新しいルートグループ",
      addFileToGroup: "グループ \"{name}\" にファイルを追加",
      importAudioToGroup: "グループ \"{name}\" にオーディオをインポート",
      importVideoToGroup: "グループ \"{name}\" にビデオをインポート",
      newChildGroup: "\"{name}\" の下に新しい子グループを作成",
      addEvent: "イベントを追加",
      transientEvent: "瞬時イベント",
      continuousEvent: "連続イベント",
      deleteEvent: "イベントを削除",
      needSpace: "少なくとも {space}ms のスペースが必要",
      availableSpace: "利用可能なスペース: {space}ms"
    },
    // === event ===
    event: {
      exceedsAudioDuration: "イベントがオーディオ持続時間を超えています。追加/編集できません！"
    },
    // === hapticFiles ===
    hapticFiles: {
      title: "ハプティックファイル"
    },
    // === inlineEdit ===
    inlineEdit: {
      groupCreateSuccess: "グループ \"{name}\" が正常に作成されました",
      groupCreateFailed: "グループの作成に失敗しました：{error}",
      groupNameEmpty: "グループ名を空にすることはできません",
      groupNamePlaceholder: "グループ名を入力",
      newGroupPlaceholder: "新しいグループ名を入力",
      newFileNamePlaceholder: "新しいファイル名を入力",
      groupRenameSuccess: "グループ名が \"{oldName}\" から \"{newName}\" に変更されました",
      groupRenameFailed: "グループ名の変更に失敗しました：{error}",
      fileNameEmpty: "ファイル名を空にすることはできません",
      fileNameMustEndWithHe: "ファイル名は .he で終わる必要があります",
      fileRenameSuccess: "ファイル名が \"{oldName}\" から \"{newName}\" に変更されました",
      fileRenameFailed: "ファイル名の変更に失敗しました：{error}"
    },
    // === groupDelete ===
    groupDelete: {
      confirmTitle: "グループ削除の確認",
      confirmMessage: "グループ \"{name}\" を削除してもよろしいですか？",
      confirmWithContentTitle: "グループとその内容の削除確認",
      confirmWithContentMessage: "グループ \"{name}\" には {count} 個のファイルが含まれています。グループとその全ての内容を削除してもよろしいですか？",
      confirmDelete: "削除",
      deleteSuccess: "グループ \"{name}\" が正常に削除されました",
      deleteWithContentSuccess: "グループ \"{name}\" とその内容が正常に削除されました",
      deleteFailed: "グループの削除に失敗しました：{error}",
      cancelled: "削除操作がキャンセルされました"
    },
    // === dragAndDrop ===
    dragAndDrop: {
      targetFileNotFound: "対象ファイルが見つかりません",
      cannotDropOnSelfOrDescendant: "アイテムを自分自身またはその子項目にドロップすることはできません",
      fileMovedToGroup: "ファイルがグループ \"{groupName}\" に移動されました",
      fileMovedToRoot: "ファイルがルートディレクトリに移動されました",
      fileMoveSuccess: "ファイルの移動が成功しました",
      groupMovedToGroup: "グループがグループ \"{groupName}\" に移動されました",
      groupMovedToRoot: "グループがルートディレクトリに移動されました",
      groupMoveSuccess: "グループの移動が成功しました",
      moveFailed: "移動に失敗しました",
      moveToRootFailed: "ルートディレクトリへの移動に失敗しました",
      unknownGroup: "不明なグループ"
    }
  },
  // === device ===
  device: {
    // === status ===
    status: {
      connected: "接続済み",
      disconnected: "未接続",
      connecting: "接続中",
      disconnecting: "切断中",
      error: "エラー",
      unknown: "不明",
      noDevices: "デバイスなし",
      title: "デバイス状態",
      total: "総デバイス数",
      errorDevices: "エラーデバイス",
      defaultDevice: "デフォルトデバイス",
      clickToOpen: "クリックしてデバイスマネージャーを開く",
      default: "デフォルト"
    },
    // === types ===
    types: {
      usb: "USB",
      wifi: "WiFi",
      bluetooth: "Bluetooth"
    },
    // === actions ===
    actions: {
      scan: "デバイススキャン",
      refresh: "更新",
      connect: "接続",
      disconnect: "切断",
      setDefault: "デフォルトに設定",
      rename: "名前変更",
      remove: "削除",
      sendFile: "ファイル送信",
      addTestDevice: "テストデバイスを追加",
      unsetDefault: "デフォルト解除"
    },
    // === messages ===
    messages: {
      scanComplete: "デバイススキャンが完了しました",
      scanFailed: "デバイススキャンに失敗しました",
      refreshSuccess: "デバイスリストが更新されました",
      refreshFailed: "更新に失敗しました",
      connectSuccess: "デバイス接続が成功しました",
      connectFailed: "デバイス接続に失敗しました",
      disconnectSuccess: "デバイス切断が成功しました",
      disconnectFailed: "デバイス切断に失敗しました",
      setDefaultSuccess: "デフォルトデバイスの設定が成功しました",
      setDefaultFailed: "デフォルトデバイスの設定に失敗しました",
      removeSuccess: "デバイス削除が成功しました",
      removeFailed: "デバイス削除に失敗しました",
      renameSuccess: "デバイス名変更が成功しました",
      renameFailed: "デバイス名変更に失敗しました",
      sendFileInfo: "ファイル送信機能は開発中です...",
      sendFileFailed: "ファイル送信に失敗しました",
      addTestDeviceSuccess: "テストデバイスの追加が成功しました",
      addTestDeviceFailed: "テストデバイスの追加に失敗しました",
      initializeFailed: "デバイスマネージャーの初期化に失敗しました"
    },
    // === filter ===
    filter: {
      deviceType: "デバイスタイプ",
      connectionStatus: "接続状態",
      searchDevices: "デバイスを検索..."
    },
    // === details ===
    details: {
      deviceId: "デバイスID",
      deviceType: "デバイスタイプ",
      connectionStatus: "接続状態",
      lastConnected: "最終接続",
      manufacturer: "製造元",
      model: "モデル"
    },
    // === rename ===
    rename: {
      title: "デバイス名変更",
      deviceName: "デバイス名",
      placeholder: "新しいデバイス名を入力",
      nameRequired: "デバイス名は必須です"
    },
    // === remove ===
    remove: {
      confirmTitle: "削除確認",
      confirmMessage: "デバイス \"{name}\" を削除してもよろしいですか？この操作は元に戻せません。"
    },
    // === transmission ===
    transmission: {
      heFile: "HEファイル",
      audioFile: "オーディオファイル",
      projectData: "プロジェクトデータ",
      deviceConfig: "デバイス設定",
      priority: {
        low: "低",
        normal: "通常",
        high: "高",
        urgent: "緊急"
      }
    },
    // === testDevice ===
    testDevice: {
      name: "テストデバイス {number}",
      manufacturer: "テスト製造元",
      model: "テストモデル"
    },
    // === errors ===
    errors: {
      unknownError: "不明なエラー",
      timeoutError: "接続タイムアウト",
      permissionDenied: "アクセス拒否",
      deviceNotFound: "デバイスが見つかりません",
      deviceBusy: "デバイスがビジー状態",
      invalidParameter: "無効なパラメータ"
    },
    // === management ===
    management: {
      title: "デバイス管理"
    }
  },
  // === errors ===
  errors: {
    unknown: "不明なエラーが発生しました",
    networkError: "ネットワークエラー",
    fileNotFound: "ファイルが見つかりません",
    operationFailed: "操作に失敗しました"
  },
  // === learning ===
  learning: {
    title: "ハプティックデザイン学習リソース",
    // === gettingStarted ===
    gettingStarted: {
      title: "ハプティックデザイン入門",
      description: "ハプティックデザインの基本と効果的なハプティックフィードバックの作成方法を学びます。",
      // === tags ===
      tags: {
        beginner: "初心者",
        tutorial: "チュートリアル"
      }
    },
    // === audioToHaptics ===
    audioToHaptics: {
      title: "オーディオからハプティックへの変換",
      description: "オーディオファイルを意味のあるハプティックフィードバックに変換するためのヒントとテクニック。",
      // === tags ===
      tags: {
        intermediate: "中級",
        tutorial: "チュートリアル"
      }
    },
    // === gaming ===
    gaming: {
      title: "ゲーム向けハプティック",
      description: "ゲームアプリケーションでハプティックフィードバックを実装するためのベストプラクティス。",
      // === tags ===
      tags: {
        gaming: "ゲーム",
        caseStudy: "ケーススタディ"
      }
    },
    // === uxDesign ===
    uxDesign: {
      title: "ハプティックUXデザイン",
      description: "モバイルアプリケーションでハプティックフィードバックを使用してユーザーエクスペリエンスを向上させる方法。",
      // === tags ===
      tags: {
        ux: "UX",
        mobile: "モバイル"
      }
    },
    // === advanced ===
    advanced: {
      title: "高度なハプティック技術",
      description: "複雑で繊細なハプティック体験を作成するための高度な技術。",
      // === tags ===
      tags: {
        advanced: "上級",
        tutorial: "チュートリアル"
      }
    },
    // === research ===
    research: {
      title: "ハプティックフィードバック研究",
      description: "ハプティックフィードバックの有効性に関する最新の研究と学術研究。",
      // === tags ===
      tags: {
        research: "研究",
        academic: "学術"
      }
    }
  },
  // === examples ===
  examples: {
    // === populationOne ===
    populationOne: {
      haptics: "8つのハプティック"
    },
    notImplemented: "機能未実装",
    notImplementedMessage: "\"{title}\" サンプルプロジェクトはまだ実装されていません。近日公開予定！"
  },
  // === update ===
  update: {
    downloading: "ダウンロード中",
    installing: "インストール中",
    updateAvailable: "アップデートが利用可能",
    newVersionAvailable: "新しいバージョンが利用可能",
    downloadNow: "今すぐダウンロード",
    installNow: "今すぐインストール",
    remindLater: "後で通知",
    installConfirmTitle: "アップデートのインストール確認",
    installConfirmMessage: "アップデートのインストールにはアプリケーションを閉じる必要があります。インストール後、新しいバージョンで自動的に再起動します。",
    installConfirmDetails: "全体のプロセスは約30秒かかります。すべての作業が保存されていることを確認してください。",
    installConfirmWarning: "プロセス中にインストーラーを手動で閉じないでください。",
    currentVersion: "現在のバージョン",
    latestVersion: "最新バージョン",
    releaseNotes: "リリースノート",
    fileSize: "ファイルサイズ",
    confirmInstallation: "インストールの確認",
    confirmInstall: "インストール確認",
    installationNotice: "インストール通知",
    applicationWillClose: "インストールを完了するためにアプリケーションが閉じられ、完了後に自動的に再起動します。",
    error: "アップデートエラー",
    installingMessage: "アップデートをインストール中、お待ちください...",
    readyToInstall: "アップデートのインストール準備完了",
    downloadComplete: "ダウンロード完了",
    downloadCompleteMessage: "アップデートファイルのダウンロードが完了しました。『今すぐインストール』をクリックしてインストールを開始してください。",
    cancelling: "キャンセル中",
    // === processManagement ===
    processManagement: {
      title: "プロセス管理の確認",
      warningTitle: "警告",
      warningMessage: "アップデートのインストールには、以下の関連プロセスを終了する必要があります。重要な作業をすべて保存していることを確認してください。",
      processListTitle: "終了するプロセス",
      closeAndInstall: "プロセスを終了してインストール",
      closeStrategyTitle: "終了方法",
      gracefulClose: "正常終了",
      gracefulCloseDesc: "プロセスを正常に終了し、プログラムがデータを保存する時間を与えます",
      forceClose: "強制終了",
      forceCloseDesc: "プロセスを即座に終了します。データが失われる可能性があります",
      critical: "重要",
      noticeTitle: "重要な注意事項",
      notice1: "プロセスを終了する前に、すべての重要な作業を保存してください",
      notice2: "重要なプロセスの終了は、システムの安定性に影響を与える可能性があります",
      notice3: "インストール完了後、アプリケーションは自動的に再起動します",
      // === processTypes ===
      processTypes: {
        MainApplication: "メインアプリ",
        EditorWindow: "エディター",
        RenderProcess: "レンダラー",
        AudioService: "オーディオサービス",
        FileMonitor: "ファイル監視",
        BackgroundService: "バックグラウンドサービス",
        ChildProcess: "子プロセス",
        Unknown: "不明なプロセス"
      }
    }
  },
  // === i18nTest ===
  i18nTest: {
    title: "国際化テスト",
    languageSwitchTest: "言語切り替えテスト",
    currentLanguage: "現在の言語",
    commonTextTest: "共通テキストテスト",
    errorMessageTest: "エラーメッセージテスト",
    languageDetectionInfo: "言語検出情報",
    exampleError: "ディスク容量不足",
    // === labels ===
    labels: {
      confirm: "確認",
      cancel: "キャンセル",
      save: "保存",
      delete: "削除",
      loading: "読み込み中",
      saveFailedExample: "保存失敗（例）",
      unknownError: "不明なエラー",
      networkError: "ネットワークエラー",
      fileNotFound: "ファイルが見つかりません"
    }
  },
  // === forceUpdate ===
  forceUpdate: {
    title: "強制アップデート",
    notice: "重要なアップデート",
    noticeMessage: "これは必須のアップデートです。アプリケーションを継続して使用するには、インストールが必要です。",
    newVersionRequired: "新しいバージョンが必要です",
    readyToInstall: "アップデートのダウンロードが完了しました、インストール準備完了",
    startDownload: "ダウンロード開始",
    installNow: "今すぐインストール",
    retryDownload: "ダウンロード再試行",
    exitApplication: "アプリケーションを終了"
  },
  // === about ===
  about: {
    title: "について",
    loading: "バージョン情報を読み込み中...",
    version: "バージョン",
    buildInfo: "ビルド情報",
    platform: "プラットフォーム",
    // === updateCheck ===
    updateCheck: {
      title: "バージョンチェック",
      checking: "アップデートを確認中...",
      checkNow: "今すぐ確認",
      newVersionAvailable: "新しいバージョンが利用可能",
      latestVersion: "最新バージョン",
      upToDate: "最新バージョンを使用中",
      checkFailed: "アップデートの確認に失敗",
      viewUpdate: "アップデートを表示"
    }
  },
  // === demo ===
  demo: {
    installProcessTitle: "インストールプロセス",
    step1Title: "ユーザーがインストールを確認",
    step1Description: "インストール確認ダイアログを表示し、アプリが閉じて自動的に再起動することを説明",
    step2Title: "インストールスクリプトの作成",
    step2Description: "インストールと再起動ロジックを含む独立したバッチスクリプトを生成",
    step3Title: "独立インストールの起動",
    step3Description: "独立したインストーラーを開始し、現在のアプリを終了",
    step4Title: "アプリケーションの自動再起動",
    step4Description: "インストール完了後、新しいバージョンを自動的に開始"
  },
  // === installer ===
  installer: {
    ready: "準備完了",
    preparing: "準備中",
    installing: "インストール中",
    success: "インストール成功",
    failed: "インストール失敗",
    cancelled: "キャンセル済み",
    completed: "完了",
    // === operations ===
    operations: {
      initializing: "初期化中",
      validating: "パッケージを検証中",
      waitingForExit: "アプリの終了を待機中",
      backingUp: "ファイルをバックアップ中",
      installing: "インストール中",
      restarting: "アプリケーションを再起動中",
      completed: "インストール完了",
      failed: "インストール失敗",
      cancelled: "キャンセル済み"
    },
    initializing: "インストーラーを初期化中",
    installSuccess: "インストールが正常に完了しました",
    unknownError: "不明なエラー",
    preparingExit: "アプリケーションの終了を準備中",
    cancelling: "キャンセル中",
    installCancelled: "インストールがキャンセルされました"
  },
  // === ota ===
  ota: {
    checkingUpdates: "更新を確認中",
    closingProcesses: "プロセスを終了中",
    downloadFailed: "ダウンロード失敗",
    downloadingUpdate: "更新をダウンロード中",
    installationCompleted: "インストール完了",
    installationFailed: "インストール失敗",
    installingUpdate: "更新をインストール中",
    noDownloadedFile: "ダウンロードされたファイルがありません",
    noUpdateInfo: "更新情報がありません",
    noUpdatesAvailable: "利用可能な更新がありません",
    preparingInstallation: "インストールを準備中",
    verificationFailed: "検証失敗",
    verifyingUpdate: "更新を検証中"
  },
  // === debug ===
  debug: {
    info: "デバッグ情報:",
    isLoading: "読み込み状態",
    hasRecentProjects: "最近のプロジェクトあり",
    recentProjectsLength: "最近のプロジェクト数",
    recentProjectsContent: "最近のプロジェクト内容",
    title: "デバッグツール",
    activated: "デバッグモードが有効になりました！",
    // === button ===
    button: {
      tooltip: "デバッグツール"
    },
    // === menu ===
    menu: {
      settings: "デバッグ設定",
      viewLogs: "ログを表示",
      openLogFolder: "ログフォルダを開く",
      exportDebugInfo: "デバッグ情報をエクスポート"
    },
    // === settings ===
    settings: {
      title: "デバッグ設定",
      enableDebug: "デバッグモードを有効にする",
      debugEnabled: "デバッグモードが有効",
      debugDisabled: "デバッグモードが無効",
      debugEnabledDesc: "現在開発モードで実行中、デバッグ機能が自動的に有効になっています。",
      debugDisabledDesc: "現在本番モードで実行中、デバッグ機能が自動的に無効になっています。",
      buildMode: "ビルドモード",
      currentConfig: "現在の設定",
      logLevel: "ログレベル",
      logOtaOperations: "OTA操作をログに記録",
      logDeviceOperations: "デバイス操作をログに記録",
      viewLogs: "ログを表示",
      openLogFolder: "ログフォルダを開く",
      resetDefault: "デフォルトにリセット",
      saveSuccess: "デバッグ設定が正常に保存されました",
      resetSuccess: "デフォルト設定にリセットしました",
      // === notice ===
      notice: {
        title: "重要な注意事項",
        sessionOnly: "デバッグ設定は現在のセッションでのみ有効で、アプリ再起動後はデフォルトに戻ります",
        performance: "詳細ログを有効にするとアプリケーションのパフォーマンスに影響する可能性があります",
        logLocation: "ログファイルはアプリケーションデータディレクトリに保存されます"
      },
      // === validation ===
      validation: {
        levelRequired: "ログレベルを選択してください"
      },
      // === errors ===
      errors: {
        loadFailed: "デバッグ設定の読み込みに失敗しました",
        saveFailed: "デバッグ設定の保存に失敗しました",
        openFolderFailed: "ログフォルダを開くのに失敗しました"
      }
    },
    // === logViewer ===
    logViewer: {
      title: "ログビューア",
      refresh: "更新",
      clear: "クリア",
      export: "エクスポート",
      maxLines: "最大行数",
      autoRefreshOn: "自動更新",
      autoRefreshOff: "手動更新",
      loading: "ログを読み込み中...",
      noLogs: "ログコンテンツがありません",
      logPath: "ログパス",
      unknown: "不明",
      lines: "行数",
      size: "サイズ",
      clearSuccess: "ログがクリアされました",
      exportSuccess: "デバッグ情報がエクスポートされました: {path}",
      // === errors ===
      errors: {
        getPathFailed: "ログファイルパスの取得に失敗しました",
        readFailed: "ログファイルの読み込みに失敗しました",
        clearFailed: "ログファイルのクリアに失敗しました",
        exportFailed: "デバッグ情報のエクスポートに失敗しました"
      }
    },
    // === errors ===
    errors: {
      openFolderFailed: "ログフォルダを開くのに失敗しました",
      exportFailed: "デバッグ情報のエクスポートに失敗しました"
    },
    exportSuccess: "デバッグ情報がエクスポートされました: {path}"
  },
  // === librtcore ===
  librtcore: {
    error: {
      general: "librtcore エラー: {message}",
      retrying: "librtcore 初期化に失敗しました。再試行中: {message}",
      manualRetryRequired: "librtcore 初期化に失敗しました。手動での再試行が必要です: {message}",
      fallbackMode: "librtcore がフォールバックモードに入りました。一部の機能が利用できない可能性があります",
      fallbackModeActive: "現在フォールバックモードです。ハプティック再生機能が制限されています",
      fallbackModeExited: "フォールバックモードを終了しました。機能が正常に復旧しました",
      resetting: "librtcore システムをリセット中...",
      retrySuccess: "再試行が成功しました。librtcore が正常に復旧しました",
      retryFailed: "再試行に失敗しました。デバイス接続または設定を確認してください",
      retryError: "再試行処理中にエラーが発生しました"
    }
  },
  // === playEffect ===
  playEffect: {
    loading: "ファイルデータを読み込み中...",
    dialogTitle: "エフェクトを再生 - {fileName}",
    dialogTitleDefault: "エフェクトを再生",
    fileInfo: "ファイル情報",
    fileName: "ファイル名",
    fileUuid: "ファイルUUID",
    filePath: "ファイルパス",
    lastModified: "最終更新",
    jsonData: "JSONデータ",
    copyJson: "JSONをコピー",
    downloadJson: "JSONをダウンロード",
    copySuccess: "JSONデータをクリップボードにコピーしました",
    copyFailed: "コピーに失敗しました",
    downloadSuccess: "JSONファイルのダウンロードが成功しました",
    downloadFailed: "ダウンロードに失敗しました",
    errorPlaying: "エフェクトの再生に失敗しました: {error}",
    notImplemented: "エフェクト再生機能は実装されていません",

    // 新しい再生制御関連
    play: "再生",
    stop: "停止",
    pause: "一時停止",
    resume: "再開",
    save: "保存",
    saveAsRtp: "RTPとして保存",
    saveToFile: "ファイルに保存",
    saving: "保存中...",
    saveSuccess: "保存成功",
    saveFailed: "保存失敗: {error}",
    selectSaveLocation: "保存場所を選択",
    saveDialogTitle: "ハプティックデータファイルを保存",
    noData: "保存するデータがありません",

    // モーター選択関連
    motorSelection: "モーター選択",
    motorModel: "モーターモデル",
    selectMotor: "モーターモデルを選択してください",
    motorLoadFailed: "モーター設定の読み込みに失敗しました。デフォルト設定を使用します",

    // サンプリングレート選択関連
    samplingRateSelection: "サンプリングレート選択",
    selectSamplingRate: "サンプリングレートを選択してください",
    samplingRate6Khz: "6KHz - 基本エフェクト",
    samplingRate8Khz: "8KHz - 一般的な選択",
    samplingRate12Khz: "12KHz - 中程度の精度",
    samplingRate24Khz: "24KHz - 高精度エフェクト",

    // 実際の周波数選択関連
    actualFrequencySelection: "実際の周波数選択",
    selectActualFrequency: "実際の周波数を選択してください",

    // キャンバス関連
    canvas: "キャンバス",
    noEvents: "イベントデータがありません",
    timeAxis: "時間軸",
    amplitudeAxis: "振幅軸",



    // コントロールパネル
    controls: "再生制御",
    settings: "設定",
    volume: "音量",
    loop: "ループ再生",

    // librtcore 統合関連
    initializingLibrtcore: "ハプティックアルゴリズムライブラリを初期化中...",
    initSuccess: "ハプティックアルゴリズムライブラリの初期化が成功しました",
    reinitSuccess: "ハプティックアルゴリズムライブラリの再初期化が成功しました",
    librtcoreError: "ハプティックアルゴリズムライブラリエラー",
    retryInit: "初期化を再試行",
    clearError: "エラーをクリア"
  }
} as const;
