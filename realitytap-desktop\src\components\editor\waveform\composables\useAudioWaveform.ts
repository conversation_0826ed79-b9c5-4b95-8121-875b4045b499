// 音频波形绘制组合式函数
// 负责音频振幅数据的加载、缓存和绘制逻辑

import { invoke } from "@tauri-apps/api/core";
import { computed, readonly, ref, type Ref } from "vue";
import { calculateOptimizedAmplitudeScale, getAmplitudeDisplayInfo, recommendAmplitudePreset, analyzeAmplitudeCharacteristics, type AmplitudeScaleConfig } from "../utils/audio-amplitude-helpers";
import { useAsyncOperationManager } from "./useAsyncOperationManager";
import { EVENT_WAVEFORM_VERTICAL_OFFSET } from "../config/waveform-constants";
import { audioLogger } from "@/utils/logger/logger";
import { analyzeOptimizationEffect } from "../utils/performance-test-audio-waveform";
import { selectOptimalLODLevel, calculateLODSampling, analyzeLODPerformance, generateLODCacheKey, type LODSelectionCriteria } from "../utils/lod-rendering-system";

// 音频振幅数据接口
export interface AudioAmplitudeData {
  samples: number[];
  sample_rate: number;
  duration_ms: number;
  max_amplitude: number;
  min_amplitude: number;
}

// 音频波形配置接口
export interface AudioWaveformConfig {
  // 坐标转换函数
  mapIntensityToYLocal: (intensity: number) => number;
  mapTimeToXLocal: (time: number) => number; // 新增：时间到X坐标的映射函数

  // 画布尺寸获取函数
  getGraphAreaWidth: () => number;
  getGraphAreaHeight: () => number;

  // 时长获取函数
  getEffectiveDuration: () => number; // 新增：获取有效时长函数

  // 虚拟滚动偏移
  virtualScrollOffset: Ref<number>;

  // 当前缩放级别
  currentZoomLevel?: Ref<number>;

  // 画布布局配置
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };

  // 音频波形显示选项
  enableSilenceFiltering?: boolean; // 是否启用无声区域过滤，默认 true
  silenceThresholdPercent?: number; // 无声阈值百分比（相对于最大振幅），默认 2%
  showWaveformBorder?: boolean; // 是否显示波形边框，默认 false
  amplitudeScale?: number; // 振幅缩放比例（0-1），默认 0.75，表示占用画布高度的75%
  amplitudeBoost?: number; // 振幅增强倍数，默认 1.5，用于放大小幅度信号
}

// 音频波形缓存项
interface AudioWaveformCacheItem {
  data: AudioAmplitudeData;
  renderedPath: Path2D;
  lastCanvasWidth: number;
  lastScrollOffset: number;
  lastZoomLevel?: number; // 添加缩放级别缓存
}

/**
 * 音频波形管理 Composable
 * 负责音频振幅数据的异步加载、缓存和绘制
 */
export function useAudioWaveform(config: AudioWaveformConfig) {
  // 使用异步操作管理器
  const asyncManager = useAsyncOperationManager();

  const {
    mapIntensityToYLocal,
    mapTimeToXLocal, // 新增：时间到X坐标的映射函数
    getGraphAreaWidth,
    getGraphAreaHeight,
    getEffectiveDuration, // 新增：有效时长获取函数
    virtualScrollOffset,
    currentZoomLevel,
    enableSilenceFiltering = true, // 默认启用无声过滤
    silenceThresholdPercent = 2, // 默认2%阈值
    showWaveformBorder = false, // 默认不显示边框
    amplitudeScale = 0.75, // 默认占用画布高度的75%
    amplitudeBoost = 1.5, // 默认1.5倍增强
  } = config;

  // 音频数据状态
  const audioData = ref<AudioAmplitudeData | null>(null);
  const isLoadingAudio = ref(false);
  const audioError = ref<string | null>(null);

  // 音频波形缓存
  const waveformCache = ref<AudioWaveformCacheItem | null>(null);

  // 计算音频时长（毫秒）
  const audioDurationMs = computed(() => {
    return audioData.value?.duration_ms || 0;
  });

  // 计算音频采样率
  const audioSampleRate = computed(() => {
    return audioData.value?.sample_rate || 44100;
  });

  // 检查是否有音频数据
  const hasAudioData = computed(() => {
    return audioData.value !== null && audioData.value.samples.length > 0;
  });

  /**
   * 异步加载音频振幅数据
   */
  const loadAudioAmplitudeData = async (projectDirPath: string, audioRelativePath: string, maxSamples?: number): Promise<void> => {
    if (!projectDirPath || !audioRelativePath) {
      audioData.value = null;
      audioError.value = null;
      return;
    }

    isLoadingAudio.value = true;
    audioError.value = null;

    try {
      audioLogger.debug("开始加载音频振幅数据", { projectDirPath, audioRelativePath, maxSamples });

      const loadPromise = invoke<AudioAmplitudeData>("get_audio_amplitude_data", {
        projectDirPath,
        audioRelativePath,
        maxSamples: maxSamples || 4096, // 默认采样点数，平衡性能和质量
      });

      // 注册 Promise 到异步管理器
      asyncManager.registerPromise(loadPromise, undefined, `加载音频振幅数据: ${audioRelativePath}`);

      const amplitudeData = await loadPromise;

      audioData.value = amplitudeData;
      waveformCache.value = null; // 清除缓存，强制重新绘制

      audioLogger.info("音频振幅数据加载成功", {
        samplesCount: amplitudeData.samples.length,
        duration: amplitudeData.duration_ms,
        sampleRate: amplitudeData.sample_rate,
      });
    } catch (error) {
      audioLogger.error("加载音频振幅数据失败", error);
      audioError.value = error instanceof Error ? error.message : "未知错误";
      audioData.value = null;
      waveformCache.value = null;
    } finally {
      isLoadingAudio.value = false;
    }
  };

  /**
   * 清除音频数据
   */
  const clearAudioData = () => {
    audioLogger.debug("useAudioWaveform: 清除音频数据");
    audioData.value = null;
    audioError.value = null;
    waveformCache.value = null;
    isLoadingAudio.value = false;
  };

  /**
   * 完全重置音频波形状态（用于组件清理）
   */
  const resetAudioWaveformState = () => {
    audioLogger.debug("完全重置音频波形状态");

    // 先清理 Path2D 对象（在设置为 null 之前）
    if (waveformCache.value?.renderedPath) {
      try {
        // 清理 Path2D 对象（如果浏览器支持）
        waveformCache.value.renderedPath = new Path2D();
      } catch (error) {
        audioLogger.warn("清理 Path2D 对象时出错", error);
      }
    }

    // 清除所有数据
    audioData.value = null;
    audioError.value = null;
    waveformCache.value = null;
    isLoadingAudio.value = false;

    audioLogger.debug("音频波形状态重置完成");
  };

  /**
   * 直接设置音频数据（用于从外部传入已加载的数据）
   */
  const setAudioData = (data: AudioAmplitudeData | null) => {
    if (data) {
      // 验证数据有效性
      if (typeof data === "object" && data.samples && Array.isArray(data.samples) && data.samples.length > 0) {
        audioLogger.debug("设置有效的音频数据", {
          samplesCount: data.samples.length,
          duration: data.duration_ms,
          sampleRate: data.sample_rate,
        });
        audioData.value = data;
        waveformCache.value = null; // 清除缓存，强制重新绘制
        audioError.value = null;
        isLoadingAudio.value = false;
      } else {
        audioLogger.warn("接收到无效的音频数据，清理状态");
        clearAudioData();
      }
    } else {
      audioLogger.debug("设置音频数据为null，清理状态");
      clearAudioData();
    }
  };

  /**
   * 创建音频波形路径
   */
  const createAudioWaveformPath = (data: AudioAmplitudeData): Path2D => {
    const path = new Path2D();
    const samples = data.samples;
    const sampleCount = samples.length;

    if (sampleCount === 0) return path;

    const durationMs = data.duration_ms;
    const graphWidth = getGraphAreaWidth();
    const graphHeight = getGraphAreaHeight();

    // 计算每个采样点对应的时间
    const timePerSample = durationMs / sampleCount;

    // 找到最大振幅用于归一化
    const maxAmp = Math.max(Math.abs(data.max_amplitude), Math.abs(data.min_amplitude));

    // 【阶段2优化】智能振幅特征分析
    const amplitudeAnalysis = analyzeAmplitudeCharacteristics(data);

    // 根据分析结果选择最优配置
    const scaleConfig: AmplitudeScaleConfig = amplitudeAnalysis.optimizedConfig;

    // 【阶段3优化】LOD级别选择
    const lodCriteria: LODSelectionCriteria = {
      amplitudeCategory: amplitudeAnalysis.amplitudeCategory,
      performanceRisk: amplitudeAnalysis.performanceRisk,
      zoomLevel: 1.0, // TODO: 从视图状态获取实际缩放级别
      viewportWidth: graphWidth,
      sampleCount,
      isRealTimeRendering: false, // TODO: 根据实际渲染模式确定
    };

    const optimalLODLevel = selectOptimalLODLevel(lodCriteria);
    const lodSampling = calculateLODSampling(samples, optimalLODLevel);

    // 使用LOD采样后的数据
    const optimizedSamples = lodSampling.samples;
    const optimizedSampleCount = optimizedSamples.length;
    const optimizedTimePerSample = durationMs / optimizedSampleCount;

    const finalAmplitudeScale = calculateOptimizedAmplitudeScale(maxAmp, graphHeight, scaleConfig);

    // 获取显示效果信息
    const displayInfo = getAmplitudeDisplayInfo(finalAmplitudeScale, maxAmp, graphHeight);

    // 推荐最佳配置（仅用于调试）
    const recommendedPreset = recommendAmplitudePreset(data.max_amplitude, data.min_amplitude, sampleCount);

    // 【性能优化】预计算性能相关变量
    const maxScaledAmplitude = finalAmplitudeScale * maxAmp;
    const minY = mapIntensityToYLocal(100); // 100%强度对应的Y坐标（顶部）
    const maxY = mapIntensityToYLocal(0);   // 0%强度对应的Y坐标（底部）
    const effectiveGraphHeight = maxY - minY;
    const needsBoundaryCheck = maxScaledAmplitude > effectiveGraphHeight;

    // 【阶段3优化】LOD性能分析
    const lodPerformance = analyzeLODPerformance(sampleCount, optimalLODLevel, optimizedSampleCount);

    // 调试信息：输出优化结果
    audioLogger.debug("音频振幅显示优化", {
      原始数据: {
        maxAmplitude: maxAmp,
        sampleCount,
        duration: durationMs + "ms",
      },
      智能分析: {
        amplitudeCategory: amplitudeAnalysis.amplitudeCategory,
        performanceRisk: amplitudeAnalysis.performanceRisk,
        recommendedPreset: amplitudeAnalysis.recommendedPreset,
        analysisDetails: amplitudeAnalysis.analysisDetails,
      },
      LOD优化: {
        selectedLevel: optimalLODLevel,
        originalSamples: sampleCount,
        optimizedSamples: optimizedSampleCount,
        reductionRatio: lodPerformance.reductionRatio.toFixed(3),
        estimatedSpeedup: lodPerformance.estimatedSpeedup,
        memoryReduction: lodPerformance.memoryReduction,
        qualityImpact: lodPerformance.qualityImpact,
      },
      缩放配置: scaleConfig,
      优化结果: {
        finalScale: finalAmplitudeScale.toFixed(3),
        heightUsage: displayInfo.heightUsagePercent,
        displayQuality: displayInfo.displayQuality,
        recommendedPreset,
      },
      性能优化: {
        needsBoundaryCheck,
        maxScaledAmplitude: maxScaledAmplitude.toFixed(2),
        effectiveGraphHeight: effectiveGraphHeight.toFixed(2),
        optimizationEnabled: true,
        intelligentPreprocessing: true,
        lodRenderingEnabled: true,
      },
    });

    // 设置无声阈值：基于配置的百分比
    const silenceThreshold = enableSilenceFiltering ? maxAmp * (silenceThresholdPercent / 100) : 0;

    // X轴位置（零基线）- 与Event波形保持一致的垂直偏移
    const centerY = mapIntensityToYLocal(0) - EVENT_WAVEFORM_VERTICAL_OFFSET; // 0对应零基线，添加垂直偏移

    // 【修复】获取有效时长，用于边界控制
    const effectiveDuration = getEffectiveDuration();

    // 【性能优化】边界值和检查条件已在上方预计算

    // 【性能优化】预计算静音阈值，避免循环中重复比较
    const silenceThresholdCached = silenceThreshold;

    let isFirstPoint = true;

    // 【阶段3优化】使用LOD优化后的样本数据
    for (let i = 0; i < optimizedSampleCount; i++) {
      // 计算原始时间索引
      const originalIndex = lodSampling.indices[i];
      const time = originalIndex * timePerSample;
      const amplitude = optimizedSamples[i];
      const absAmplitude = Math.abs(amplitude);

      // 【修复】添加时间边界检查，确保不超出有效时长范围
      if (time > effectiveDuration) {
        // 如果音频时间超出有效时长，停止绘制
        break;
      }

      // 【修复】使用标准坐标映射函数，确保与时间刻度线坐标系统一
      // 这样可以确保音频波形和时间刻度线使用相同的时长基准和坐标计算方式
      const x = mapTimeToXLocal(time);

      // 只绘制可见区域内的点
      if (x < -50 || x > graphWidth + 50) continue;

      // 【性能优化】无声区域过滤：使用预计算的阈值
      let y: number;
      if (absAmplitude < silenceThresholdCached) {
        // 无声区域：直接绘制到X轴
        y = centerY;
      } else {
        // 有声区域：使用优化的振幅缩放算法
        const normalizedAmplitude = absAmplitude * finalAmplitudeScale;
        y = centerY - normalizedAmplitude; // 向上绘制，确保从基线开始

        // 【性能优化】条件边界检查：只在需要时执行裁剪
        if (needsBoundaryCheck) {
          y = Math.max(y, minY);
        }
      }

      if (isFirstPoint) {
        path.moveTo(x, centerY);
        isFirstPoint = false;
      }

      path.lineTo(x, y);
    }

    // 【修复】回到X轴 - 使用有效时长作为结束点，确保不超过时间刻度线
    if (!isFirstPoint) {
      // 计算音频结束时间和有效时长的最小值，确保不超出时间刻度线范围
      const effectiveEndTime = Math.min(durationMs, effectiveDuration);
      const endX = mapTimeToXLocal(effectiveEndTime);
      path.lineTo(endX, centerY);
    }

    // 【性能优化】分析优化效果（包含智能预处理和LOD）
    analyzeOptimizationEffect(
      needsBoundaryCheck,
      maxScaledAmplitude,
      effectiveGraphHeight,
      optimizedSampleCount, // 使用优化后的样本数
      amplitudeAnalysis.amplitudeCategory,
      amplitudeAnalysis.performanceRisk
    );

    return path;
  };

  /**
   * 检查是否需要重新生成波形路径
   */
  const shouldRegenerateWaveform = (): boolean => {
    if (!waveformCache.value || !audioData.value) return true;

    const currentWidth = getGraphAreaWidth();
    const currentScrollOffset = virtualScrollOffset.value;
    const currentZoom = currentZoomLevel?.value || 1.0;

    // 检查音频数据是否发生变化（通过比较数据引用）
    const dataChanged = waveformCache.value.data !== audioData.value;

    // 检查缩放级别是否发生变化
    const zoomChanged = waveformCache.value.lastZoomLevel !== currentZoom;

    return (
      dataChanged ||
      waveformCache.value.lastCanvasWidth !== currentWidth ||
      Math.abs(waveformCache.value.lastScrollOffset - currentScrollOffset) > 10 || // 滚动超过10像素时重新生成
      zoomChanged // 缩放级别变化时重新生成
    );
  };

  /**
   * 绘制音频波形
   */
  const drawAudioWaveform = (ctx: CanvasRenderingContext2D): void => {
    if (!hasAudioData.value || !audioData.value) return;

    // 检查是否需要重新生成波形路径
    if (shouldRegenerateWaveform()) {
      const path = createAudioWaveformPath(audioData.value);
      const currentZoom = currentZoomLevel?.value || 1.0;
      waveformCache.value = {
        data: audioData.value,
        renderedPath: path,
        lastCanvasWidth: getGraphAreaWidth(),
        lastScrollOffset: virtualScrollOffset.value,
        lastZoomLevel: currentZoom, // 保存当前缩放级别
      };
    }

    if (!waveformCache.value) return;

    // 设置绘制样式
    ctx.save();
    ctx.fillStyle = "rgba(74, 106, 146, 0.15)"; // 半透明填充
    ctx.lineCap = "round";
    ctx.lineJoin = "round";

    // 绘制填充区域
    ctx.fill(waveformCache.value.renderedPath);

    // 根据配置决定是否绘制边框
    if (showWaveformBorder) {
      ctx.strokeStyle = "#4A90E2"; // 蓝色音频波形边框
      ctx.lineWidth = 1;
      ctx.stroke(waveformCache.value.renderedPath);
    }

    ctx.restore();
  };

  /**
   * 获取音频波形在指定时间的振幅
   */
  const getAmplitudeAtTime = (timeMs: number): number => {
    if (!audioData.value || audioData.value.samples.length === 0) return 0;

    const sampleIndex = Math.floor((timeMs / audioData.value.duration_ms) * audioData.value.samples.length);
    const clampedIndex = Math.max(0, Math.min(sampleIndex, audioData.value.samples.length - 1));

    return Math.abs(audioData.value.samples[clampedIndex]);
  };

  return {
    // 状态
    audioData: readonly(audioData),
    isLoadingAudio: readonly(isLoadingAudio),
    audioError: readonly(audioError),
    hasAudioData,
    audioDurationMs,
    audioSampleRate,

    // 方法
    loadAudioAmplitudeData,
    clearAudioData,
    resetAudioWaveformState,
    setAudioData,
    drawAudioWaveform,
    getAmplitudeAtTime,
  };
}
