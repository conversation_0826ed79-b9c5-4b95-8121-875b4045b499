// Common types and enums
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>, PartialEq)]
#[serde(rename_all = "camelCase")]
pub enum HeFormatVersion {
    V1,
    V2,
}

// Placeholder for potential merge options (can be detailed later if needed)
#[derive(Serial<PERSON>, Deserialize, Debug, <PERSON><PERSON>, Default)]
#[serde(rename_all = "camelCase")]
pub struct MergeOptions {
    pub conflict_resolution: Option<String>, // e.g., "overwrite", "rename", "skip"
}

// Placeholder for potential export options (can be detailed later if needed)
#[derive(Serialize, Deserialize, Debug, <PERSON>lone, Default)]
#[serde(rename_all = "camelCase")]
pub struct ExportOptions {
    pub target_format: Option<HeFormatVersion>, // V1 or V2
    pub export_as_template: bool,
    pub include_audio: bool,
    // selection: Option<Selection>, // Requires defining Selection struct (e.g., list of UUIDs)
}

// Placeholder for .he content (can be a string or a more complex struct later)
#[derive(Serialize, Deserialize, Debug, <PERSON>lone)]
pub struct HeContent {
    // Example fields, replace with actual V1/V2 structure later
    pub format: HeFormatVersion,
    pub data: serde_json::Value, // Use Value for flexibility initially
}
