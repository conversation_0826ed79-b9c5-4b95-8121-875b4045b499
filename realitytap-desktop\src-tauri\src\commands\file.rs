// File management commands
use crate::{
    commands::app_data::add_to_recent_projects,
    error::{Error, Result},
    models,
    project::{self, HAPTICS_DIR_NAME, AUDIO_DIR_NAME, io::VIDEO_DIR_NAME},
    utils::validation::validate_file_name,
};
use chrono::Utc;
use std::fs;
use std::path::PathBuf;
use tauri;
use uuid::Uuid;

#[tauri::command]
pub async fn add_file_to_project(
    project_dir_path: String,
    source_file_path_str: String,
    target_relative_path_str: String,
    is_audio_file: bool,
) -> Result<models::Project> {
    log::info!(
        "Adding file '{}' to project '{}' as '{}' (is_audio: {})",
        source_file_path_str,
        project_dir_path,
        target_relative_path_str,
        is_audio_file
    );

    let proj_dir_path = PathBuf::from(&project_dir_path);
    let source_file_path = PathBuf::from(&source_file_path_str);

    if !source_file_path.exists() {
        return Err(Error::NotFound(format!(
            "Source file not found: {:?}",
            source_file_path
        )));
    }

    let mut project = project::read_project_directory(&proj_dir_path)?;

    let target_base_dir_name = if is_audio_file {
        AUDIO_DIR_NAME
    } else {
        HAPTICS_DIR_NAME
    };

    let target_file_project_relative_path =
        PathBuf::from(target_base_dir_name).join(&target_relative_path_str);
    let target_file_absolute_path = proj_dir_path.join(&target_file_project_relative_path);

    // Create parent directories if they don't exist
    if let Some(parent_dir) = target_file_absolute_path.parent() {
        fs::create_dir_all(parent_dir)
            .map_err(|e| Error::Io(format!("Could not create target subdirectories: {:?}", e)))?;
    }

    // Copy the file
    fs::copy(&source_file_path, &target_file_absolute_path).map_err(|e| {
        Error::Io(format!(
            "Failed to copy file from {:?} to {:?}: {}",
            source_file_path, target_file_absolute_path, e
        ))
    })?;

    // Create a new HapticFile entry
    let file_name = PathBuf::from(&target_relative_path_str)
        .file_name()
        .unwrap_or_default()
        .to_str()
        .unwrap_or_default()
        .to_string();

    let new_file_entry = models::HapticFile {
        file_uuid: Uuid::new_v4(),
        name: file_name,
        path: target_relative_path_str.clone(),
        group: None,
        format_version: models::HeFormatVersion::V2,
        create_time: Utc::now(),
        last_modified_time: Utc::now(),
        description: "".to_string(),
        version: "1.0.0".to_string(),
        associated_audio: None,
        associated_video: None,
        audio_info: None,
        tags: vec![],
        file_type: if is_audio_file {
            "audio".to_string()
        } else {
            "he".to_string()
        },
        zoom_level: None,
    };

    project.files.push(new_file_entry);
    project.last_modified_time = Utc::now();

    project::write_project_directory(&project, &proj_dir_path)?;
    add_to_recent_projects(&project, &project_dir_path)?;

    Ok(project)
}

#[tauri::command]
pub async fn remove_file_from_project(
    project_dir_path: String,
    file_uuid_str: String,
) -> Result<models::Project> {
    log::info!(
        "Removing file UUID '{}' from project '{}'",
        file_uuid_str,
        project_dir_path
    );

    let proj_dir_path = PathBuf::from(&project_dir_path);
    let file_uuid_to_remove = Uuid::parse_str(&file_uuid_str)
        .map_err(|_| Error::ValidationError("Invalid file UUID format".to_string()))?;

    let mut project = project::read_project_directory(&proj_dir_path)?;

    let file_to_remove_path: Option<PathBuf>;
    let mut associated_audio_to_remove_path: Option<PathBuf> = None;
    let mut associated_video_to_remove_path: Option<PathBuf> = None;

    if let Some(index) = project
        .files
        .iter()
        .position(|f| f.file_uuid == file_uuid_to_remove)
    {
        let file_entry = &project.files[index];

        // Path for the main file
        file_to_remove_path = Some(proj_dir_path.join(&file_entry.path));

        if let Some(audio_rel_path_str) = &file_entry.associated_audio {
            let audio_file_rel_path =
                PathBuf::from(AUDIO_DIR_NAME).join(audio_rel_path_str);
            associated_audio_to_remove_path = Some(proj_dir_path.join(audio_file_rel_path));
        }

        if let Some(video_rel_path_str) = &file_entry.associated_video {
            let video_file_rel_path =
                PathBuf::from(VIDEO_DIR_NAME).join(video_rel_path_str);
            associated_video_to_remove_path = Some(proj_dir_path.join(video_file_rel_path));
        }

        project.files.remove(index);
    } else {
        file_to_remove_path = None;
        return Err(Error::NotFound(format!(
            "File with UUID '{}' not found in project.",
            file_uuid_str
        )));
    }

    // Delete the actual files from file system
    if let Some(path) = file_to_remove_path {
        if path.exists() {
            fs::remove_file(&path)
                .map_err(|e| Error::Io(format!("Failed to delete file {:?}: {}", path, e)))?;
            log::info!("Deleted file: {:?}", path);
        } else {
            log::warn!("File to delete not found (already removed?): {:?}", path);
        }
    }
    
    if let Some(path) = associated_audio_to_remove_path {
        if path.exists() {
            fs::remove_file(&path).map_err(|e| {
                Error::Io(format!(
                    "Failed to delete associated audio file {:?}: {}",
                    path, e
                ))
            })?;
            log::info!("Deleted associated audio file: {:?}", path);
        } else {
            log::warn!(
                "Associated audio file to delete not found (already removed?): {:?}",
                path
            );
        }
    }

    if let Some(path) = associated_video_to_remove_path {
        if path.exists() {
            fs::remove_file(&path).map_err(|e| {
                Error::Io(format!(
                    "Failed to delete associated video file {:?}: {}",
                    path, e
                ))
            })?;
            log::info!("Deleted associated video file: {:?}", path);
        } else {
            log::warn!(
                "Associated video file to delete not found (already removed?): {:?}",
                path
            );
        }
    }

    project.last_modified_time = Utc::now();
    project::write_project_directory(&project, &proj_dir_path)?;
    add_to_recent_projects(&project, &project_dir_path)?;

    Ok(project)
}

#[tauri::command]
pub async fn create_he_file_in_project(
    project_dir_path: String,
    file_name: String,
    he_content: String,
    group_id: Option<String>,
    associated_audio: Option<String>,
    associated_video: Option<String>,
) -> Result<models::Project> {
    log::info!(
        "Creating new HE file '{}' in project '{}', group_id: {:?}, associated_audio: {:?}, associated_video: {:?}",
        file_name,
        project_dir_path,
        group_id,
        associated_audio,
        associated_video
    );

    let proj_dir_path_buf = PathBuf::from(&project_dir_path);

    // Validate JSON content
    let parsed_content: serde_json::Value = match serde_json::from_str(&he_content) {
        Ok(content) => content,
        Err(e) => {
            return Err(Error::ValidationError(format!(
                "Invalid HE file content: {}",
                e
            )))
        }
    };

    // Use parameter associated_audio first, fallback to JSON content
    let associated_audio = if let Some(audio) = associated_audio {
        Some(audio)
    } else {
        parsed_content
            .get("associatedAudio")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string())
    };

    let mut project = project::read_project_directory(&proj_dir_path_buf)?;

    // Parse group_id and get group path
    let mut group_path_str: Option<String> = None;
    let group_uuid_opt: Option<Uuid> = match group_id {
        Some(id_str) => {
            if id_str.trim().is_empty() {
                None
            } else {
                let parsed_uuid = Uuid::parse_str(&id_str)
                    .map_err(|_| Error::ValidationError("Invalid group UUID format".to_string()))?;
                let found_group = project.groups.iter().find(|g| g.group_uuid == parsed_uuid);
                if let Some(g) = found_group {
                    group_path_str = Some(g.path.clone());
                    Some(parsed_uuid)
                } else {
                    return Err(Error::NotFound(format!(
                        "Group with UUID '{}' not found",
                        id_str
                    )));
                }
            }
        }
        None => None,
    };

    // Check for name conflicts within the same group
    let name_conflict = project.files.iter().any(|f| {
        f.name == file_name && f.group == group_uuid_opt
    });

    if name_conflict {
        let group_name_for_error = group_path_str.as_deref().unwrap_or("project root");
        return Err(Error::ValidationError(format!(
            "File name '{}' already exists in group '{}'",
            file_name, group_name_for_error
        )));
    }

    // Build file path for project.json storage
    let mut final_file_entry_path_parts: Vec<String> =
        vec![HAPTICS_DIR_NAME.to_string()];
    if let Some(g_path) = &group_path_str {
        if !g_path.is_empty() {
            g_path.split('/').for_each(|part| {
                if !part.is_empty() {
                    final_file_entry_path_parts.push(part.to_string());
                }
            });
        }
    }
    final_file_entry_path_parts.push(file_name.clone());

    // Build target file path using cross-platform path handling
    let mut target_file_relative_path = PathBuf::new();
    for part in &final_file_entry_path_parts {
        target_file_relative_path = target_file_relative_path.join(part);
    }

    // Build target file absolute path
    let target_file_abs_path = proj_dir_path_buf.join(&target_file_relative_path);

    // Ensure target directory exists
    if let Some(parent_dir) = target_file_abs_path.parent() {
        fs::create_dir_all(parent_dir).map_err(|e| {
            Error::Io(format!(
                "Could not create target directory {:?}: {}",
                parent_dir, e
            ))
        })?;
    }

    // Write file content
    fs::write(&target_file_abs_path, he_content).map_err(|e| {
        Error::Io(format!(
            "Failed to write file {:?}: {}",
            target_file_abs_path, e
        ))
    })?;

    // Create new HapticFile entry
    // Convert the relative path to a string using forward slashes for consistency in the database
    let final_file_entry_path_str = target_file_relative_path
        .to_string_lossy()
        .replace('\\', "/");

    let new_file_entry = models::HapticFile {
        file_uuid: Uuid::new_v4(),
        name: file_name.clone(),
        path: final_file_entry_path_str,
        group: group_uuid_opt,
        format_version: models::HeFormatVersion::V1,
        create_time: Utc::now(),
        last_modified_time: Utc::now(),
        description: "New haptic effect".to_string(),
        version: "1.0.0".to_string(),
        associated_audio,
        associated_video,
        audio_info: None,
        tags: vec![],
        file_type: "he".to_string(),
        zoom_level: None,
    };

    project.files.push(new_file_entry);
    project.last_modified_time = Utc::now();

    project::write_project_directory(&project, &proj_dir_path_buf)?;
    add_to_recent_projects(&project, &project_dir_path)?;

    Ok(project)
}

#[tauri::command]
pub async fn read_file_content_command(file_path: String) -> Result<String> {
    log::info!("Attempting to read file content for path: {}", file_path);
    match fs::read_to_string(&file_path) {
        Ok(content) => {
            log::info!("Successfully read file content for path: {}", file_path);
            Ok(content)
        }
        Err(e) => {
            log::error!("Failed to read file {}: {}", file_path, e);
            Err(Error::Io(format!(
                "Failed to read file {}: {}",
                file_path, e
            )))
        }
    }
}

#[tauri::command]
pub async fn write_file_content_command(
    project_dir_path: String,
    file_path: String,
    content: String,
) -> Result<()> {
    log::info!(
        "Attempting to write file content for path: {} in project: {}",
        file_path,
        project_dir_path
    );

    let proj_dir_path = PathBuf::from(&project_dir_path);
    let full_file_path = proj_dir_path.join(&file_path);

    // Ensure target directory exists
    if let Some(parent_dir) = full_file_path.parent() {
        fs::create_dir_all(parent_dir).map_err(|e| {
            Error::Io(format!(
                "Could not create target directory {:?}: {}",
                parent_dir, e
            ))
        })?;
    }

    match fs::write(&full_file_path, content) {
        Ok(_) => {
            log::info!(
                "Successfully wrote file content for path: {:?}",
                full_file_path
            );
            Ok(())
        }
        Err(e) => {
            log::error!("Failed to write file {:?}: {}", full_file_path, e);
            Err(Error::Io(format!(
                "Failed to write file {:?}: {}",
                full_file_path, e
            )))
        }
    }
}

#[tauri::command]
pub async fn rename_file_command(
    project_dir_path: String,
    file_uuid_str: String,
    new_file_name_str: String,
) -> Result<models::Project> {
    log::info!(
        "Attempting to rename file UUID '{}' to '{}' in project '{}'",
        file_uuid_str,
        new_file_name_str,
        project_dir_path
    );

    let proj_dir_path_buf = PathBuf::from(&project_dir_path);
    let mut project = project::read_project_directory(&proj_dir_path_buf)?;

    let file_uuid_to_rename = Uuid::parse_str(&file_uuid_str)
        .map_err(|_| Error::ValidationError("无效的文件UUID格式".to_string()))?;

    let file_index = project
        .files
        .iter()
        .position(|f| f.file_uuid == file_uuid_to_rename)
        .ok_or_else(|| Error::NotFound(format!("未找到UUID为 {} 的文件", file_uuid_str)))?;

    let old_file_name = project.files[file_index].name.clone();
    let old_file_path_str = project.files[file_index].path.clone();

    // Validate new file name
    let validated_name = validate_file_name(&new_file_name_str, Some("he"))?;

    if validated_name == old_file_name {
        log::info!("文件名未更改，跳过重命名。");
        return Ok(project);
    }

    // Check for conflicts in the same directory
    let file_path_obj = PathBuf::from(&old_file_path_str);
    let parent_dir_of_file = file_path_obj
        .parent()
        .map_or("", |p| p.to_str().unwrap_or(""));

    for (i, f) in project.files.iter().enumerate() {
        if i == file_index {
            continue; // Skip self
        }
        let other_file_path_obj = PathBuf::from(&f.path);
        let other_parent_dir = other_file_path_obj
            .parent()
            .map_or("", |p| p.to_str().unwrap_or(""));
        if f.name == validated_name && parent_dir_of_file == other_parent_dir {
            return Err(Error::ValidationError(format!(
                "在同一位置已存在名为 '{}' 的文件",
                validated_name
            )));
        }
    }

    // Determine physical paths
    let old_physical_path = proj_dir_path_buf.join(&old_file_path_str);

    // Construct new file path
    let new_file_path_str = if let Some(idx) = old_file_path_str.rfind('/') {
        format!("{}/{}", &old_file_path_str[..idx], validated_name)
    } else {
        let old_path_pathbuf = PathBuf::from(&old_file_path_str);
        if let Some(parent_path) = old_path_pathbuf.parent() {
            parent_path
                .join(&validated_name)
                .to_str()
                .unwrap_or_default()
                .to_string()
        } else {
            validated_name.clone()
        }
    };

    let new_physical_path = proj_dir_path_buf.join(&new_file_path_str);

    if !old_physical_path.exists() {
        log::error!(
            "Physical file not found for rename: {:?}",
            old_physical_path
        );
        return Err(Error::NotFound(format!(
            "要重命名的物理文件 {:?} 不存在",
            old_physical_path.display()
        )));
    }

    // Filesystem rename
    if let Err(e) = fs::rename(&old_physical_path, &new_physical_path) {
        log::error!(
            "Failed to rename physical file from {:?} to {:?}: {}",
            old_physical_path,
            new_physical_path,
            e
        );
        return Err(Error::Io(format!("重命名物理文件失败: {}", e)));
    }
    log::info!(
        "Successfully renamed physical file: {:?} -> {:?}",
        old_physical_path,
        new_physical_path
    );

    // Update metadata
    project.files[file_index].name = validated_name;
    project.files[file_index].path = new_file_path_str;
    project.files[file_index].last_modified_time = Utc::now();
    project.last_modified_time = Utc::now();

    // Save project.json
    if let Err(e) = project::write_project_directory(&project, &proj_dir_path_buf) {
        log::error!("Failed to write project.json after renaming file: {}", e);
        if fs::rename(&new_physical_path, &old_physical_path).is_err() {
            log::error!(
                "CRITICAL: Failed to revert file rename after project.json save failure. Project may be inconsistent. Old: {:?}, New: {:?}",
                old_physical_path, new_physical_path
            );
        }
        return Err(e);
    }
    log::info!("Successfully updated project.json after file rename.");

    Ok(project)
}

/// 显示文件保存对话框
///
/// 弹出系统文件保存对话框，让用户选择保存路径
/// 默认扩展名为 .bin，支持覆盖现有文件
#[tauri::command]
pub async fn show_save_file_dialog(
    app: tauri::AppHandle,
    default_filename: Option<String>,
) -> Result<Option<String>> {
    log::info!("显示文件保存对话框，默认文件名: {:?}", default_filename);

    // 使用 Tauri 2.0 的 dialog 插件
    use tauri_plugin_dialog::DialogExt;

    use tokio::sync::oneshot;

    // 设置默认文件名
    let default_name = if let Some(filename) = default_filename {
        // 确保文件名有 .bin 扩展名
        if filename.ends_with(".bin") {
            filename
        } else {
            format!("{}.bin", filename)
        }
    } else {
        "haptic_data.bin".to_string()
    };

    // 创建一个 oneshot channel 来等待对话框结果
    let (tx, rx) = oneshot::channel();

    // 使用 Tauri dialog 插件显示保存对话框
    app.dialog()
        .file()
        .set_title("保存触觉数据文件")
        .set_file_name(&default_name)
        .add_filter("Binary Files", &["bin"])
        .add_filter("All Files", &["*"])
        .save_file(move |file_path| {
            let _ = tx.send(file_path);
        });

    // 等待对话框结果
    match rx.await {
        Ok(Some(path)) => {
            let path_str = path.to_string();
            log::info!("用户选择保存路径: {}", path_str);
            Ok(Some(path_str))
        }
        Ok(None) => {
            log::info!("用户取消了文件保存对话框");
            Ok(None)
        }
        Err(_) => {
            log::error!("文件保存对话框通信错误");
            Err(Error::InvalidOperation("文件保存对话框通信错误".to_string()))
        }
    }
}
